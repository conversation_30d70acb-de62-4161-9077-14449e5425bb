# MULLS Lidar Calibration

MULLS (Multi-resolution Unified Lidar Localization and Surface mapping System) is an advanced lidar point cloud registration tool designed for high-precision calibration and alignment of lidar data.

## Features

- Supports multiple input formats: .pcd, .las, .ply, .txt, .h5
- Ground segmentation and feature extraction
- Global registration using TEASER++ or RANSAC
- ICP-based local refinement
- Real-time visualization options
- Highly configurable with numerous parameters for fine-tuning

## Dependencies

The project depends on several libraries:
- PCL (Point Cloud Library) > 1.7
- GFlags
- GLog
- TEASER++ (optional, but recommended)
- OpenCV2 (optional)
- HDF5 (optional)
- Ceres (optional)

See the CMakeLists.txt for a complete list of dependencies and build options.

## Building

To build the project:

1. Install required dependencies:
   ```bash
   ./install_dep_lib.sh  # Follow instructions in this script
   ```

2. Configure and build with CMake:
   ```bash
   mkdir build
   cd build
   cmake ..
   make -j$(nproc)
   ```

## Usage

The main executable is `mulls_reg`, which performs pairwise point cloud registration.

### Example Command

```bash
./bin/mulls_reg \
  --point_cloud_1_path=path/to/target.pcd \
  --point_cloud_2_path=path/to/source.pcd \
  --output_point_cloud_path=path/to/output.pcd \
  --realtime_viewer_on=true \
  --cloud_1_down_res=0.05 \
  --cloud_2_down_res=0.05 \
  --gf_grid_size=3.0 \
  --teaser_on=true
```

### Demo

A demo is provided in the `demo_data` directory:

```bash
./run_mulls_reg.sh
```

This will register two point clouds from the demo data and save the result.

## Parameters

The tool has numerous parameters to control various aspects of the registration process. See the source code or run with `--help` for a complete list.

## License

This project is open-source. See LICENSE file for details.