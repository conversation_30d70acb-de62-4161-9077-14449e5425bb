//
// Multi-point cloud registration using MULLS-ICP and TEASER
// Dependent 3rd Libs: PCL (>1.7), glog, gflags, TEASER (optional)
// By Yue Pan
//

#include "dataio.hpp"
#include "cfilter.hpp"
#include "cregistration.hpp"
#include "map_viewer.h"
// #include "build_pose_graph.h"
#include "utility.hpp"

#include <glog/logging.h>
#include <gflags/gflags.h>

using namespace lo;

//GFLAG Template: DEFINE_TYPE(Flag_variable_name, default_value, "Comments")
//file path
DEFINE_string(point_cloud_paths, "", "Comma-separated list of point cloud file paths");
DEFINE_string(output_point_cloud_path, "", "Registered source pointcloud file path");
DEFINE_string(reference_point_cloud_idx, "0", "Index of the reference point cloud (0-based)");
DEFINE_string(appro_coordinate_file, "", "File used to store the approximate station coordinate");
//key parameters
DEFINE_double(cloud_down_res, 0.0, "voxel size(m) of downsample for all point clouds");
DEFINE_double(gf_grid_size, 2.0, "grid size(m) of ground segmentation");
DEFINE_double(gf_in_grid_h_thre, 0.3, "height threshold(m) above the lowest point in a grid for ground segmentation");
DEFINE_double(gf_neigh_grid_h_thre, 2.2, "height threshold(m) among neighbor grids for ground segmentation");
DEFINE_double(gf_max_h, DBL_MAX, "max height(m) allowed for ground point");
DEFINE_int32(gf_ground_down_rate, 10, "downsampling decimation rate for target ground point cloud");
DEFINE_int32(gf_nonground_down_rate, 3, "downsampling decimation rate for nonground point cloud");
DEFINE_int32(dist_inverse_sampling_method, 0, "use distance inverse sampling or not (0: disabled, 1: linear weight, 2: quadratic weight)");
DEFINE_double(unit_dist, 15.0, "distance that corresponds to unit weight in inverse distance downsampling");
DEFINE_bool(pca_distance_adpative_on, false, "enable the distance adaptive pca or not. It is preferred to be on if the point cloud is collected by a spinning scanner located at origin point");
DEFINE_double(pca_neighbor_radius, 1.0, "pca neighborhood searching radius(m) for target point cloud");
DEFINE_int32(pca_neighbor_count, 30, "use only the k nearest neighbor in the r-neighborhood to do PCA");
DEFINE_double(linearity_thre, 0.6, "pca linearity threshold");
DEFINE_double(planarity_thre, 0.6, "pca planarity threshold");
DEFINE_double(curvature_thre, 0.1, "pca local stability threshold");
DEFINE_int32(corr_num, 3000, "fixed number of the correspondence for global coarse registration (only when fixed_num_corr_on is on).");
DEFINE_bool(reciprocal_corr_on, false, "Using reciprocal correspondence");
DEFINE_bool(fixed_num_corr_on, false, "Using fixed number correspondence (best k matches)");
DEFINE_double(corr_dis_thre, 2.0, "distance threshold between correspondence points");
DEFINE_int32(reg_max_iter_num, 25, "max iteration number for icp-based registration");
DEFINE_double(converge_tran, 0.001, "convergence threshold for translation (in m)");
DEFINE_double(converge_rot_d, 0.01, "convergence threshold for rotation (in degree)");
DEFINE_double(heading_change_step_degree, 15, "The step for the rotation of heading");
DEFINE_bool(is_global_reg, true, "Allow the global registration without good enough initial guess or not");
DEFINE_bool(teaser_on, false, "Using TEASER++ or to do the global coarse registration or not (using RANSAC instead)");
//visualizer parameters
DEFINE_bool(realtime_viewer_on, false, "Launch the real-time registration(correspondence) viewer or not");
DEFINE_int32(screen_width, 1920, "monitor horizontal resolution (pixel)");
DEFINE_int32(screen_height, 1080, "monitor vertical resolution (pixel)");
DEFINE_double(vis_intensity_scale, 256.0, "max intensity value of your data");

int main(int argc, char **argv)
{
    google::ParseCommandLineFlags(&argc, &argv, true);
    google::InitGoogleLogging("Mylog_testreg");
    LOG(INFO) << "Launch the program!";
    LOG(INFO) << "Logging is written to " << FLAGS_log_dir;

    pcl::console::setVerbosityLevel(pcl::console::L_ALWAYS); //Ban pcl warnings

    CHECK(FLAGS_point_cloud_paths != "") << "Need to specify at least two point clouds.";
    CHECK(FLAGS_output_point_cloud_path != "") << "Need to specify where to save the registered point cloud.";

    // Parse point cloud paths
    std::vector<std::string> point_cloud_files;
    std::stringstream ss(FLAGS_point_cloud_paths);
    std::string path;
    while (std::getline(ss, path, ',')) {
        if (!path.empty()) {
            point_cloud_files.push_back(path);
        }
    }

    CHECK(point_cloud_files.size() >= 2) << "Need at least two point clouds for registration.";

    // Parse reference point cloud index
    int ref_idx = std::stoi(FLAGS_reference_point_cloud_idx);
    CHECK(ref_idx >= 0 && ref_idx < static_cast<int>(point_cloud_files.size())) << "Invalid reference point cloud index.";

    // Import configuration
    float vf_downsample_resolution = FLAGS_cloud_down_res;
    float gf_grid_resolution = FLAGS_gf_grid_size;
    float gf_max_grid_height_diff = FLAGS_gf_in_grid_h_thre;
    float gf_neighbor_height_diff = FLAGS_gf_neigh_grid_h_thre;
    float gf_max_height = FLAGS_gf_max_h;
    int ground_down_rate = FLAGS_gf_ground_down_rate;
    int nonground_down_rate = FLAGS_gf_nonground_down_rate;
    int dist_inv_sampling_method = FLAGS_dist_inverse_sampling_method;
    float dist_inv_sampling_dist = FLAGS_unit_dist;
    bool pca_distance_adpative_on = FLAGS_pca_distance_adpative_on;
    float pca_neigh_r = FLAGS_pca_neighbor_radius;
    int pca_neigh_k = FLAGS_pca_neighbor_count;
    float pca_linearity_thre = FLAGS_linearity_thre;
    float pca_planarity_thre = FLAGS_planarity_thre;
    float pca_curvature_thre = FLAGS_curvature_thre;
    int feature_correspondence_num = FLAGS_corr_num;
    float reg_corr_dis_thre = FLAGS_corr_dis_thre;
    float converge_tran = FLAGS_converge_tran;
    float converge_rot_d = FLAGS_converge_rot_d;
    int max_iteration_num = FLAGS_reg_max_iter_num;
    float heading_step_d = FLAGS_heading_change_step_degree;
    bool launch_realtime_viewer = FLAGS_realtime_viewer_on;
    bool global_registration_on = FLAGS_is_global_reg;
    bool teaser_on = FLAGS_teaser_on;
    float pca_linearity_thre_down = pca_linearity_thre + 0.1;
    float pca_planarity_thre_down = pca_planarity_thre + 0.1;
    float keypoint_nms_radius = 0.25 * pca_neigh_r;

    DataIo<Point_T> dataio;
    MapViewer<Point_T> viewer(FLAGS_vis_intensity_scale, 0, 1, 1);
    CFilter<Point_T> cfilter;
    CRegistration<Point_T> creg;

    boost::shared_ptr<pcl::visualization::PCLVisualizer> feature_viewer;
    boost::shared_ptr<pcl::visualization::PCLVisualizer> reg_viewer;

    if (launch_realtime_viewer)
    {
        feature_viewer = boost::shared_ptr<pcl::visualization::PCLVisualizer>(new pcl::visualization::PCLVisualizer("Feature Viewer"));
        reg_viewer = boost::shared_ptr<pcl::visualization::PCLVisualizer>(new pcl::visualization::PCLVisualizer("Registration Viewer"));
        viewer.set_interactive_events(feature_viewer, FLAGS_screen_width, FLAGS_screen_width);
        viewer.set_interactive_events(reg_viewer, FLAGS_screen_width, FLAGS_screen_width);
    }

    // Load all point clouds
    std::vector<cloudblock_Ptr> cloudblocks;
    for (const auto& filename : point_cloud_files) {
        cloudblock_Ptr cblock(new cloudblock_t());
        cblock->filename = filename;
        dataio.read_pc_cloud_block(cblock, true);
        cloudblocks.push_back(cblock);

        // Extract feature points
        cfilter.extract_semantic_pts(cblock, vf_downsample_resolution, gf_grid_resolution,
                                     gf_max_grid_height_diff, gf_neighbor_height_diff,
                                     gf_max_height, ground_down_rate, nonground_down_rate,
                                     pca_neigh_r, pca_neigh_k, pca_linearity_thre,
                                     pca_planarity_thre, pca_curvature_thre,
                                     pca_linearity_thre_down, pca_planarity_thre_down,
                                     pca_distance_adpative_on, dist_inv_sampling_method,
                                     dist_inv_sampling_dist);

        if (global_registration_on) // refine keypoints
        {
            cfilter.non_max_suppress(cblock->pc_vertex, keypoint_nms_radius);
        }
    }

    // Use the reference point cloud as target and register all others to it
    Eigen::Matrix4d identity = Eigen::Matrix4d::Identity();
    for (size_t i = 0; i < cloudblocks.size(); ++i) {
        if (i == ref_idx) continue; // Skip the reference point cloud itself

        constraint_t reg_con;
        bool is_tpc_spc_in_order = creg.determine_source_target_cloud(cloudblocks[ref_idx], cloudblocks[i], reg_con);

        Eigen::Matrix4d init_mat = identity;

        if (global_registration_on)
        {
            pcTPtr target_cor(new pcT()), source_cor(new pcT());
            creg.find_feature_correspondence_ncc(reg_con.block1->pc_vertex, reg_con.block2->pc_vertex,
                                                 target_cor, source_cor,
                                                 FLAGS_fixed_num_corr_on, feature_correspondence_num,
                                                 FLAGS_reciprocal_corr_on);

            if (teaser_on)
                creg.coarse_reg_teaser(target_cor, source_cor, init_mat, 4.0 * keypoint_nms_radius);
            else
                creg.coarse_reg_ransac(target_cor, source_cor, init_mat, 4.0 * keypoint_nms_radius);
        }

        // Perform fine registration (ICP)
        creg.mm_lls_icp(reg_con, max_iteration_num, reg_corr_dis_thre,
                        converge_tran, converge_rot_d, 0.25 * reg_corr_dis_thre,
                        1.1, "111110", "1101", 1.0, 0.1, 0.1, 0.1, init_mat);

        // Transform the source point cloud to target's coordinate system
        Eigen::Matrix4d Trans1_2_inverse = reg_con.Trans1_2.inverse();
        pcTPtr pc_s_tran(new pcT());
        if (is_tpc_spc_in_order) {
            pcl::transformPointCloud(*reg_con.block2->pc_raw, *pc_s_tran, reg_con.Trans1_2);
        } else {
            pcl::transformPointCloud(*reg_con.block1->pc_raw, *pc_s_tran, Trans1_2_inverse);
        }
        cloudblocks[i]->pc_raw_w = pc_s_tran;
    }

    // Merge all point clouds into one
    pcXYZRGBPtr pc_merged(new pcXYZRGB);

    // Assign colors to each point cloud for visualization
    std::vector<Eigen::Vector3i> colors = {
        {255, 0, 0},   // Red
        {0, 255, 0},   // Green
        {0, 0, 255},   // Blue
        {255, 255, 0}, // Yellow
        {255, 0, 255}, // Magenta
        {0, 255, 255}, // Cyan
        {128, 128, 0}, // Olive
        {128, 0, 128}  // Purple
    };

    for (size_t i = 0; i < cloudblocks.size(); ++i) {
        pcXYZRGBPtr pc_colored(new pcXYZRGB);
        pcl::copyPointCloud(*(i == ref_idx ? cloudblocks[i]->pc_raw : cloudblocks[i]->pc_raw_w), *pc_colored);

        Eigen::Vector3i color = colors[i % colors.size()];
        for (auto& pt : pc_colored->points) {
            pt.r = color[0];
            pt.g = color[1];
            pt.b = color[2];
        }

        *pc_merged += *pc_colored;
    }

    // Save the merged point cloud
    if (pcl::io::savePCDFileBinary(FLAGS_output_point_cloud_path, *pc_merged) == -1) {
        PCL_ERROR("Couldn't write file\n");
    } else {
        LOG(INFO) << "Merged point cloud saved to " << FLAGS_output_point_cloud_path;
    }

    return 0;
}