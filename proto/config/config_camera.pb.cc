// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/config/config_camera.proto

#include "proto/config/config_camera.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_PinholeCamera;
}  // namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto
namespace protobuf_proto_2fmath_2fgeo_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Transformation3d;
}  // namespace protobuf_proto_2fmath_2fgeo_2eproto
namespace esurfing {
namespace proto {
namespace config {
class PinholeCameraDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PinholeCamera>
      _instance;
} _PinholeCamera_default_instance_;
class ConfigCamerasDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConfigCameras>
      _instance;
} _ConfigCameras_default_instance_;
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto {
static void InitDefaultsPinholeCamera() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_PinholeCamera_default_instance_;
    new (ptr) ::esurfing::proto::config::PinholeCamera();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::PinholeCamera::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_PinholeCamera =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsPinholeCamera}, {
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Transformation3d.base,}};

static void InitDefaultsConfigCameras() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_ConfigCameras_default_instance_;
    new (ptr) ::esurfing::proto::config::ConfigCameras();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::ConfigCameras::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ConfigCameras =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsConfigCameras}, {
      &protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::scc_info_PinholeCamera.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_PinholeCamera.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ConfigCameras.base);
}

::google::protobuf::Metadata file_level_metadata[2];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, image_width_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, image_height_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, frame_rate_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, tf_vehicle_camera_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, fx_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, fy_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, cx_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, cy_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, radial_distortion_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, flip_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, crop_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, topic_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, time_synced_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::PinholeCamera, usage_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigCameras, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigCameras, cameras_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::esurfing::proto::config::PinholeCamera)},
  { 20, -1, sizeof(::esurfing::proto::config::ConfigCameras)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_PinholeCamera_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_ConfigCameras_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "proto/config/config_camera.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n proto/config/config_camera.proto\022\025esur"
      "fing.proto.config\032\024proto/math/geo.proto\""
      "\357\002\n\rPinholeCamera\022\014\n\004name\030\001 \001(\014\022\023\n\013image"
      "_width\030\002 \001(\005\022\024\n\014image_height\030\003 \001(\005\022\022\n\nfr"
      "ame_rate\030\004 \001(\001\022@\n\021tf_vehicle_camera\030\005 \001("
      "\0132%.esurfing.proto.math.Transformation3d"
      "\022\n\n\002fx\030\006 \001(\001\022\n\n\002fy\030\007 \001(\001\022\n\n\002cx\030\010 \001(\001\022\n\n\002"
      "cy\030\t \001(\001\022\031\n\021radial_distortion\030\n \003(\001\022\014\n\004f"
      "lip\030\013 \001(\010\022\014\n\004crop\030\014 \001(\010\022\r\n\005topic\030\r \001(\014\022\023"
      "\n\013time_synced\030\016 \001(\010\022\r\n\005usage\030\017 \001(\005\"5\n\005Us"
      "age\022\013\n\007UNKNOWN\020\000\022\n\n\006VISION\020\001\022\n\n\006SIGNAL\020\002"
      "\022\007\n\003ALL\020\003\"F\n\rConfigCameras\0225\n\007cameras\030\001 "
      "\003(\0132$.esurfing.proto.config.PinholeCamer"
      "aB:\n\014proto.configB\014CameraConfigP\000Z\032proto"
      "/config/config_camerab\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 589);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "proto/config/config_camera.proto", &protobuf_RegisterTypes);
  ::protobuf_proto_2fmath_2fgeo_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto
namespace esurfing {
namespace proto {
namespace config {
const ::google::protobuf::EnumDescriptor* PinholeCamera_Usage_descriptor() {
  protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::file_level_enum_descriptors[0];
}
bool PinholeCamera_Usage_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const PinholeCamera_Usage PinholeCamera::UNKNOWN;
const PinholeCamera_Usage PinholeCamera::VISION;
const PinholeCamera_Usage PinholeCamera::SIGNAL;
const PinholeCamera_Usage PinholeCamera::ALL;
const PinholeCamera_Usage PinholeCamera::Usage_MIN;
const PinholeCamera_Usage PinholeCamera::Usage_MAX;
const int PinholeCamera::Usage_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void PinholeCamera::InitAsDefaultInstance() {
  ::esurfing::proto::config::_PinholeCamera_default_instance_._instance.get_mutable()->tf_vehicle_camera_ = const_cast< ::esurfing::proto::math::Transformation3d*>(
      ::esurfing::proto::math::Transformation3d::internal_default_instance());
}
void PinholeCamera::clear_tf_vehicle_camera() {
  if (GetArenaNoVirtual() == NULL && tf_vehicle_camera_ != NULL) {
    delete tf_vehicle_camera_;
  }
  tf_vehicle_camera_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PinholeCamera::kNameFieldNumber;
const int PinholeCamera::kImageWidthFieldNumber;
const int PinholeCamera::kImageHeightFieldNumber;
const int PinholeCamera::kFrameRateFieldNumber;
const int PinholeCamera::kTfVehicleCameraFieldNumber;
const int PinholeCamera::kFxFieldNumber;
const int PinholeCamera::kFyFieldNumber;
const int PinholeCamera::kCxFieldNumber;
const int PinholeCamera::kCyFieldNumber;
const int PinholeCamera::kRadialDistortionFieldNumber;
const int PinholeCamera::kFlipFieldNumber;
const int PinholeCamera::kCropFieldNumber;
const int PinholeCamera::kTopicFieldNumber;
const int PinholeCamera::kTimeSyncedFieldNumber;
const int PinholeCamera::kUsageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PinholeCamera::PinholeCamera()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::scc_info_PinholeCamera.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.PinholeCamera)
}
PinholeCamera::PinholeCamera(const PinholeCamera& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      radial_distortion_(from.radial_distortion_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  topic_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.topic().size() > 0) {
    topic_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.topic_);
  }
  if (from.has_tf_vehicle_camera()) {
    tf_vehicle_camera_ = new ::esurfing::proto::math::Transformation3d(*from.tf_vehicle_camera_);
  } else {
    tf_vehicle_camera_ = NULL;
  }
  ::memcpy(&image_width_, &from.image_width_,
    static_cast<size_t>(reinterpret_cast<char*>(&usage_) -
    reinterpret_cast<char*>(&image_width_)) + sizeof(usage_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.PinholeCamera)
}

void PinholeCamera::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  topic_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tf_vehicle_camera_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&usage_) -
      reinterpret_cast<char*>(&tf_vehicle_camera_)) + sizeof(usage_));
}

PinholeCamera::~PinholeCamera() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.PinholeCamera)
  SharedDtor();
}

void PinholeCamera::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  topic_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tf_vehicle_camera_;
}

void PinholeCamera::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* PinholeCamera::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PinholeCamera& PinholeCamera::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::scc_info_PinholeCamera.base);
  return *internal_default_instance();
}


void PinholeCamera::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.PinholeCamera)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  radial_distortion_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  topic_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && tf_vehicle_camera_ != NULL) {
    delete tf_vehicle_camera_;
  }
  tf_vehicle_camera_ = NULL;
  ::memset(&image_width_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&usage_) -
      reinterpret_cast<char*>(&image_width_)) + sizeof(usage_));
  _internal_metadata_.Clear();
}

bool PinholeCamera::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.PinholeCamera)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bytes name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_name()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 image_width = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &image_width_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 image_height = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &image_height_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double frame_rate = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &frame_rate_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .esurfing.proto.math.Transformation3d tf_vehicle_camera = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tf_vehicle_camera()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double fx = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fx_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double fy = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(57u /* 57 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fy_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double cx = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(65u /* 65 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &cx_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double cy = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(73u /* 73 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &cy_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated double radial_distortion = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_radial_distortion())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(81u /* 81 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 82u, input, this->mutable_radial_distortion())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool flip = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &flip_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool crop = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &crop_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes topic = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_topic()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool time_synced = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &time_synced_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 usage = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &usage_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.PinholeCamera)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.PinholeCamera)
  return false;
#undef DO_
}

void PinholeCamera::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.PinholeCamera)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->name(), output);
  }

  // int32 image_width = 2;
  if (this->image_width() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->image_width(), output);
  }

  // int32 image_height = 3;
  if (this->image_height() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->image_height(), output);
  }

  // double frame_rate = 4;
  if (this->frame_rate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->frame_rate(), output);
  }

  // .esurfing.proto.math.Transformation3d tf_vehicle_camera = 5;
  if (this->has_tf_vehicle_camera()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_tf_vehicle_camera(), output);
  }

  // double fx = 6;
  if (this->fx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->fx(), output);
  }

  // double fy = 7;
  if (this->fy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->fy(), output);
  }

  // double cx = 8;
  if (this->cx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->cx(), output);
  }

  // double cy = 9;
  if (this->cy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(9, this->cy(), output);
  }

  // repeated double radial_distortion = 10;
  if (this->radial_distortion_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(10, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _radial_distortion_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteDoubleArray(
      this->radial_distortion().data(), this->radial_distortion_size(), output);
  }

  // bool flip = 11;
  if (this->flip() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(11, this->flip(), output);
  }

  // bool crop = 12;
  if (this->crop() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(12, this->crop(), output);
  }

  // bytes topic = 13;
  if (this->topic().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      13, this->topic(), output);
  }

  // bool time_synced = 14;
  if (this->time_synced() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(14, this->time_synced(), output);
  }

  // int32 usage = 15;
  if (this->usage() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->usage(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.PinholeCamera)
}

::google::protobuf::uint8* PinholeCamera::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.PinholeCamera)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes name = 1;
  if (this->name().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->name(), target);
  }

  // int32 image_width = 2;
  if (this->image_width() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->image_width(), target);
  }

  // int32 image_height = 3;
  if (this->image_height() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->image_height(), target);
  }

  // double frame_rate = 4;
  if (this->frame_rate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->frame_rate(), target);
  }

  // .esurfing.proto.math.Transformation3d tf_vehicle_camera = 5;
  if (this->has_tf_vehicle_camera()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_tf_vehicle_camera(), deterministic, target);
  }

  // double fx = 6;
  if (this->fx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->fx(), target);
  }

  // double fy = 7;
  if (this->fy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->fy(), target);
  }

  // double cx = 8;
  if (this->cx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->cx(), target);
  }

  // double cy = 9;
  if (this->cy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(9, this->cy(), target);
  }

  // repeated double radial_distortion = 10;
  if (this->radial_distortion_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      10,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _radial_distortion_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->radial_distortion_, target);
  }

  // bool flip = 11;
  if (this->flip() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(11, this->flip(), target);
  }

  // bool crop = 12;
  if (this->crop() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(12, this->crop(), target);
  }

  // bytes topic = 13;
  if (this->topic().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        13, this->topic(), target);
  }

  // bool time_synced = 14;
  if (this->time_synced() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(14, this->time_synced(), target);
  }

  // int32 usage = 15;
  if (this->usage() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->usage(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.PinholeCamera)
  return target;
}

size_t PinholeCamera::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.PinholeCamera)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated double radial_distortion = 10;
  {
    unsigned int count = static_cast<unsigned int>(this->radial_distortion_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _radial_distortion_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // bytes name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->name());
  }

  // bytes topic = 13;
  if (this->topic().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->topic());
  }

  // .esurfing.proto.math.Transformation3d tf_vehicle_camera = 5;
  if (this->has_tf_vehicle_camera()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tf_vehicle_camera_);
  }

  // int32 image_width = 2;
  if (this->image_width() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->image_width());
  }

  // int32 image_height = 3;
  if (this->image_height() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->image_height());
  }

  // double frame_rate = 4;
  if (this->frame_rate() != 0) {
    total_size += 1 + 8;
  }

  // double fx = 6;
  if (this->fx() != 0) {
    total_size += 1 + 8;
  }

  // double fy = 7;
  if (this->fy() != 0) {
    total_size += 1 + 8;
  }

  // double cx = 8;
  if (this->cx() != 0) {
    total_size += 1 + 8;
  }

  // double cy = 9;
  if (this->cy() != 0) {
    total_size += 1 + 8;
  }

  // bool flip = 11;
  if (this->flip() != 0) {
    total_size += 1 + 1;
  }

  // bool crop = 12;
  if (this->crop() != 0) {
    total_size += 1 + 1;
  }

  // bool time_synced = 14;
  if (this->time_synced() != 0) {
    total_size += 1 + 1;
  }

  // int32 usage = 15;
  if (this->usage() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->usage());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PinholeCamera::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.PinholeCamera)
  GOOGLE_DCHECK_NE(&from, this);
  const PinholeCamera* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PinholeCamera>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.PinholeCamera)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.PinholeCamera)
    MergeFrom(*source);
  }
}

void PinholeCamera::MergeFrom(const PinholeCamera& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.PinholeCamera)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  radial_distortion_.MergeFrom(from.radial_distortion_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.topic().size() > 0) {

    topic_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.topic_);
  }
  if (from.has_tf_vehicle_camera()) {
    mutable_tf_vehicle_camera()->::esurfing::proto::math::Transformation3d::MergeFrom(from.tf_vehicle_camera());
  }
  if (from.image_width() != 0) {
    set_image_width(from.image_width());
  }
  if (from.image_height() != 0) {
    set_image_height(from.image_height());
  }
  if (from.frame_rate() != 0) {
    set_frame_rate(from.frame_rate());
  }
  if (from.fx() != 0) {
    set_fx(from.fx());
  }
  if (from.fy() != 0) {
    set_fy(from.fy());
  }
  if (from.cx() != 0) {
    set_cx(from.cx());
  }
  if (from.cy() != 0) {
    set_cy(from.cy());
  }
  if (from.flip() != 0) {
    set_flip(from.flip());
  }
  if (from.crop() != 0) {
    set_crop(from.crop());
  }
  if (from.time_synced() != 0) {
    set_time_synced(from.time_synced());
  }
  if (from.usage() != 0) {
    set_usage(from.usage());
  }
}

void PinholeCamera::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.PinholeCamera)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PinholeCamera::CopyFrom(const PinholeCamera& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.PinholeCamera)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PinholeCamera::IsInitialized() const {
  return true;
}

void PinholeCamera::Swap(PinholeCamera* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PinholeCamera::InternalSwap(PinholeCamera* other) {
  using std::swap;
  radial_distortion_.InternalSwap(&other->radial_distortion_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  topic_.Swap(&other->topic_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tf_vehicle_camera_, other->tf_vehicle_camera_);
  swap(image_width_, other->image_width_);
  swap(image_height_, other->image_height_);
  swap(frame_rate_, other->frame_rate_);
  swap(fx_, other->fx_);
  swap(fy_, other->fy_);
  swap(cx_, other->cx_);
  swap(cy_, other->cy_);
  swap(flip_, other->flip_);
  swap(crop_, other->crop_);
  swap(time_synced_, other->time_synced_);
  swap(usage_, other->usage_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata PinholeCamera::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ConfigCameras::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ConfigCameras::kCamerasFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ConfigCameras::ConfigCameras()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::scc_info_ConfigCameras.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.ConfigCameras)
}
ConfigCameras::ConfigCameras(const ConfigCameras& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      cameras_(from.cameras_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.ConfigCameras)
}

void ConfigCameras::SharedCtor() {
}

ConfigCameras::~ConfigCameras() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.ConfigCameras)
  SharedDtor();
}

void ConfigCameras::SharedDtor() {
}

void ConfigCameras::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ConfigCameras::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ConfigCameras& ConfigCameras::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::scc_info_ConfigCameras.base);
  return *internal_default_instance();
}


void ConfigCameras::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.ConfigCameras)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cameras_.Clear();
  _internal_metadata_.Clear();
}

bool ConfigCameras::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.ConfigCameras)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_cameras()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.ConfigCameras)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.ConfigCameras)
  return false;
#undef DO_
}

void ConfigCameras::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.ConfigCameras)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->cameras_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->cameras(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.ConfigCameras)
}

::google::protobuf::uint8* ConfigCameras::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.ConfigCameras)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->cameras_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->cameras(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.ConfigCameras)
  return target;
}

size_t ConfigCameras::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.ConfigCameras)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->cameras_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->cameras(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConfigCameras::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.ConfigCameras)
  GOOGLE_DCHECK_NE(&from, this);
  const ConfigCameras* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ConfigCameras>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.ConfigCameras)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.ConfigCameras)
    MergeFrom(*source);
  }
}

void ConfigCameras::MergeFrom(const ConfigCameras& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.ConfigCameras)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cameras_.MergeFrom(from.cameras_);
}

void ConfigCameras::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.ConfigCameras)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConfigCameras::CopyFrom(const ConfigCameras& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.ConfigCameras)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigCameras::IsInitialized() const {
  return true;
}

void ConfigCameras::Swap(ConfigCameras* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ConfigCameras::InternalSwap(ConfigCameras* other) {
  using std::swap;
  CastToBase(&cameras_)->InternalSwap(CastToBase(&other->cameras_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ConfigCameras::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::PinholeCamera* Arena::CreateMaybeMessage< ::esurfing::proto::config::PinholeCamera >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::PinholeCamera >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::ConfigCameras* Arena::CreateMaybeMessage< ::esurfing::proto::config::ConfigCameras >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::ConfigCameras >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)
