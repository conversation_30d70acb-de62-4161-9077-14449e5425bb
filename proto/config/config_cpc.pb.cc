// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/config/config_cpc.proto

#include "proto/config/config_cpc.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_PinholeCamera;
}  // namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto
namespace protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_RelativePose;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_RelativePositions;
}  // namespace protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto
namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5flidar_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Lidar;
}  // namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto
namespace protobuf_proto_2fconfig_2fconfig_5fradar_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fradar_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Radar;
}  // namespace protobuf_proto_2fconfig_2fconfig_5fradar_2eproto
namespace protobuf_proto_2fmath_2fgeo_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Transformation3d;
}  // namespace protobuf_proto_2fmath_2fgeo_2eproto
namespace esurfing {
namespace proto {
namespace config {
class RelativePoseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RelativePose>
      _instance;
} _RelativePose_default_instance_;
class RelativePositionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RelativePositions>
      _instance;
} _RelativePositions_default_instance_;
class ConfigCpcDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConfigCpc>
      _instance;
} _ConfigCpc_default_instance_;
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto {
static void InitDefaultsRelativePose() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_RelativePose_default_instance_;
    new (ptr) ::esurfing::proto::config::RelativePose();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::RelativePose::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RelativePose =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRelativePose}, {
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Transformation3d.base,}};

static void InitDefaultsRelativePositions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_RelativePositions_default_instance_;
    new (ptr) ::esurfing::proto::config::RelativePositions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::RelativePositions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RelativePositions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRelativePositions}, {
      &protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_RelativePose.base,}};

static void InitDefaultsConfigCpc() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_ConfigCpc_default_instance_;
    new (ptr) ::esurfing::proto::config::ConfigCpc();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::ConfigCpc::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_ConfigCpc =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsConfigCpc}, {
      &protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::scc_info_PinholeCamera.base,
      &protobuf_proto_2fconfig_2fconfig_5fradar_2eproto::scc_info_Radar.base,
      &protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_Lidar.base,
      &protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_RelativePositions.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_RelativePose.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RelativePositions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ConfigCpc.base);
}

::google::protobuf::Metadata file_level_metadata[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::RelativePose, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::RelativePose, sensor_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::RelativePose, sensor_tf_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::RelativePositions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::RelativePositions, origin_sensor_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::RelativePositions, sensors_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigCpc, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigCpc, cameras_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigCpc, radars_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigCpc, lidars_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigCpc, relative_positions_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::esurfing::proto::config::RelativePose)},
  { 7, -1, sizeof(::esurfing::proto::config::RelativePositions)},
  { 14, -1, sizeof(::esurfing::proto::config::ConfigCpc)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_RelativePose_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_RelativePositions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_ConfigCpc_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "proto/config/config_cpc.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\035proto/config/config_cpc.proto\022\025esurfin"
      "g.proto.config\032\024proto/math/geo.proto\032 pr"
      "oto/config/config_camera.proto\032\037proto/co"
      "nfig/config_radar.proto\032\037proto/config/co"
      "nfig_lidar.proto\"]\n\014RelativePose\022\023\n\013sens"
      "or_name\030\001 \001(\014\0228\n\tsensor_tf\030\002 \001(\0132%.esurf"
      "ing.proto.math.Transformation3d\"e\n\021Relat"
      "ivePositions\022\032\n\022origin_sensor_name\030\001 \001(\014"
      "\0224\n\007sensors\030\002 \003(\0132#.esurfing.proto.confi"
      "g.RelativePose\"\344\001\n\tConfigCpc\0225\n\007cameras\030"
      "\001 \003(\0132$.esurfing.proto.config.PinholeCam"
      "era\022,\n\006radars\030\002 \003(\0132\034.esurfing.proto.con"
      "fig.Radar\022,\n\006lidars\030\003 \003(\0132\034.esurfing.pro"
      "to.config.Lidar\022D\n\022relative_positions\030\004 "
      "\001(\0132(.esurfing.proto.config.RelativePosi"
      "tionsB4\n\014proto.configB\tCpcConfigP\000Z\027prot"
      "o/config/config_cpcb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 667);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "proto/config/config_cpc.proto", &protobuf_RegisterTypes);
  ::protobuf_proto_2fmath_2fgeo_2eproto::AddDescriptors();
  ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::AddDescriptors();
  ::protobuf_proto_2fconfig_2fconfig_5fradar_2eproto::AddDescriptors();
  ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto
namespace esurfing {
namespace proto {
namespace config {

// ===================================================================

void RelativePose::InitAsDefaultInstance() {
  ::esurfing::proto::config::_RelativePose_default_instance_._instance.get_mutable()->sensor_tf_ = const_cast< ::esurfing::proto::math::Transformation3d*>(
      ::esurfing::proto::math::Transformation3d::internal_default_instance());
}
void RelativePose::clear_sensor_tf() {
  if (GetArenaNoVirtual() == NULL && sensor_tf_ != NULL) {
    delete sensor_tf_;
  }
  sensor_tf_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RelativePose::kSensorNameFieldNumber;
const int RelativePose::kSensorTfFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RelativePose::RelativePose()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_RelativePose.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.RelativePose)
}
RelativePose::RelativePose(const RelativePose& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  sensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.sensor_name().size() > 0) {
    sensor_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.sensor_name_);
  }
  if (from.has_sensor_tf()) {
    sensor_tf_ = new ::esurfing::proto::math::Transformation3d(*from.sensor_tf_);
  } else {
    sensor_tf_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.RelativePose)
}

void RelativePose::SharedCtor() {
  sensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sensor_tf_ = NULL;
}

RelativePose::~RelativePose() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.RelativePose)
  SharedDtor();
}

void RelativePose::SharedDtor() {
  sensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete sensor_tf_;
}

void RelativePose::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RelativePose::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RelativePose& RelativePose::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_RelativePose.base);
  return *internal_default_instance();
}


void RelativePose::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.RelativePose)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sensor_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && sensor_tf_ != NULL) {
    delete sensor_tf_;
  }
  sensor_tf_ = NULL;
  _internal_metadata_.Clear();
}

bool RelativePose::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.RelativePose)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bytes sensor_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_sensor_name()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .esurfing.proto.math.Transformation3d sensor_tf = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_sensor_tf()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.RelativePose)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.RelativePose)
  return false;
#undef DO_
}

void RelativePose::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.RelativePose)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes sensor_name = 1;
  if (this->sensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->sensor_name(), output);
  }

  // .esurfing.proto.math.Transformation3d sensor_tf = 2;
  if (this->has_sensor_tf()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_sensor_tf(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.RelativePose)
}

::google::protobuf::uint8* RelativePose::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.RelativePose)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes sensor_name = 1;
  if (this->sensor_name().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->sensor_name(), target);
  }

  // .esurfing.proto.math.Transformation3d sensor_tf = 2;
  if (this->has_sensor_tf()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_sensor_tf(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.RelativePose)
  return target;
}

size_t RelativePose::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.RelativePose)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bytes sensor_name = 1;
  if (this->sensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->sensor_name());
  }

  // .esurfing.proto.math.Transformation3d sensor_tf = 2;
  if (this->has_sensor_tf()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *sensor_tf_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RelativePose::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.RelativePose)
  GOOGLE_DCHECK_NE(&from, this);
  const RelativePose* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RelativePose>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.RelativePose)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.RelativePose)
    MergeFrom(*source);
  }
}

void RelativePose::MergeFrom(const RelativePose& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.RelativePose)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.sensor_name().size() > 0) {

    sensor_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.sensor_name_);
  }
  if (from.has_sensor_tf()) {
    mutable_sensor_tf()->::esurfing::proto::math::Transformation3d::MergeFrom(from.sensor_tf());
  }
}

void RelativePose::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.RelativePose)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RelativePose::CopyFrom(const RelativePose& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.RelativePose)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RelativePose::IsInitialized() const {
  return true;
}

void RelativePose::Swap(RelativePose* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RelativePose::InternalSwap(RelativePose* other) {
  using std::swap;
  sensor_name_.Swap(&other->sensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(sensor_tf_, other->sensor_tf_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RelativePose::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RelativePositions::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RelativePositions::kOriginSensorNameFieldNumber;
const int RelativePositions::kSensorsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RelativePositions::RelativePositions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_RelativePositions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.RelativePositions)
}
RelativePositions::RelativePositions(const RelativePositions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      sensors_(from.sensors_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  origin_sensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.origin_sensor_name().size() > 0) {
    origin_sensor_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.origin_sensor_name_);
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.RelativePositions)
}

void RelativePositions::SharedCtor() {
  origin_sensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

RelativePositions::~RelativePositions() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.RelativePositions)
  SharedDtor();
}

void RelativePositions::SharedDtor() {
  origin_sensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RelativePositions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RelativePositions::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RelativePositions& RelativePositions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_RelativePositions.base);
  return *internal_default_instance();
}


void RelativePositions::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.RelativePositions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sensors_.Clear();
  origin_sensor_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool RelativePositions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.RelativePositions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bytes origin_sensor_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_origin_sensor_name()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .esurfing.proto.config.RelativePose sensors = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_sensors()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.RelativePositions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.RelativePositions)
  return false;
#undef DO_
}

void RelativePositions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.RelativePositions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes origin_sensor_name = 1;
  if (this->origin_sensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->origin_sensor_name(), output);
  }

  // repeated .esurfing.proto.config.RelativePose sensors = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->sensors_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->sensors(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.RelativePositions)
}

::google::protobuf::uint8* RelativePositions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.RelativePositions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes origin_sensor_name = 1;
  if (this->origin_sensor_name().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->origin_sensor_name(), target);
  }

  // repeated .esurfing.proto.config.RelativePose sensors = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->sensors_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->sensors(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.RelativePositions)
  return target;
}

size_t RelativePositions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.RelativePositions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.config.RelativePose sensors = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->sensors_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->sensors(static_cast<int>(i)));
    }
  }

  // bytes origin_sensor_name = 1;
  if (this->origin_sensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->origin_sensor_name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RelativePositions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.RelativePositions)
  GOOGLE_DCHECK_NE(&from, this);
  const RelativePositions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RelativePositions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.RelativePositions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.RelativePositions)
    MergeFrom(*source);
  }
}

void RelativePositions::MergeFrom(const RelativePositions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.RelativePositions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  sensors_.MergeFrom(from.sensors_);
  if (from.origin_sensor_name().size() > 0) {

    origin_sensor_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.origin_sensor_name_);
  }
}

void RelativePositions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.RelativePositions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RelativePositions::CopyFrom(const RelativePositions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.RelativePositions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RelativePositions::IsInitialized() const {
  return true;
}

void RelativePositions::Swap(RelativePositions* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RelativePositions::InternalSwap(RelativePositions* other) {
  using std::swap;
  CastToBase(&sensors_)->InternalSwap(CastToBase(&other->sensors_));
  origin_sensor_name_.Swap(&other->origin_sensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RelativePositions::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ConfigCpc::InitAsDefaultInstance() {
  ::esurfing::proto::config::_ConfigCpc_default_instance_._instance.get_mutable()->relative_positions_ = const_cast< ::esurfing::proto::config::RelativePositions*>(
      ::esurfing::proto::config::RelativePositions::internal_default_instance());
}
void ConfigCpc::clear_cameras() {
  cameras_.Clear();
}
void ConfigCpc::clear_radars() {
  radars_.Clear();
}
void ConfigCpc::clear_lidars() {
  lidars_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ConfigCpc::kCamerasFieldNumber;
const int ConfigCpc::kRadarsFieldNumber;
const int ConfigCpc::kLidarsFieldNumber;
const int ConfigCpc::kRelativePositionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ConfigCpc::ConfigCpc()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_ConfigCpc.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.ConfigCpc)
}
ConfigCpc::ConfigCpc(const ConfigCpc& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      cameras_(from.cameras_),
      radars_(from.radars_),
      lidars_(from.lidars_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_relative_positions()) {
    relative_positions_ = new ::esurfing::proto::config::RelativePositions(*from.relative_positions_);
  } else {
    relative_positions_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.ConfigCpc)
}

void ConfigCpc::SharedCtor() {
  relative_positions_ = NULL;
}

ConfigCpc::~ConfigCpc() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.ConfigCpc)
  SharedDtor();
}

void ConfigCpc::SharedDtor() {
  if (this != internal_default_instance()) delete relative_positions_;
}

void ConfigCpc::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ConfigCpc::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ConfigCpc& ConfigCpc::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::scc_info_ConfigCpc.base);
  return *internal_default_instance();
}


void ConfigCpc::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.ConfigCpc)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cameras_.Clear();
  radars_.Clear();
  lidars_.Clear();
  if (GetArenaNoVirtual() == NULL && relative_positions_ != NULL) {
    delete relative_positions_;
  }
  relative_positions_ = NULL;
  _internal_metadata_.Clear();
}

bool ConfigCpc::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.ConfigCpc)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_cameras()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .esurfing.proto.config.Radar radars = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_radars()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .esurfing.proto.config.Lidar lidars = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_lidars()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .esurfing.proto.config.RelativePositions relative_positions = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_relative_positions()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.ConfigCpc)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.ConfigCpc)
  return false;
#undef DO_
}

void ConfigCpc::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.ConfigCpc)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->cameras_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->cameras(static_cast<int>(i)),
      output);
  }

  // repeated .esurfing.proto.config.Radar radars = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->radars_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->radars(static_cast<int>(i)),
      output);
  }

  // repeated .esurfing.proto.config.Lidar lidars = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->lidars_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->lidars(static_cast<int>(i)),
      output);
  }

  // .esurfing.proto.config.RelativePositions relative_positions = 4;
  if (this->has_relative_positions()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_relative_positions(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.ConfigCpc)
}

::google::protobuf::uint8* ConfigCpc::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.ConfigCpc)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->cameras_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->cameras(static_cast<int>(i)), deterministic, target);
  }

  // repeated .esurfing.proto.config.Radar radars = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->radars_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->radars(static_cast<int>(i)), deterministic, target);
  }

  // repeated .esurfing.proto.config.Lidar lidars = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->lidars_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->lidars(static_cast<int>(i)), deterministic, target);
  }

  // .esurfing.proto.config.RelativePositions relative_positions = 4;
  if (this->has_relative_positions()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_relative_positions(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.ConfigCpc)
  return target;
}

size_t ConfigCpc::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.ConfigCpc)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->cameras_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->cameras(static_cast<int>(i)));
    }
  }

  // repeated .esurfing.proto.config.Radar radars = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->radars_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->radars(static_cast<int>(i)));
    }
  }

  // repeated .esurfing.proto.config.Lidar lidars = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->lidars_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->lidars(static_cast<int>(i)));
    }
  }

  // .esurfing.proto.config.RelativePositions relative_positions = 4;
  if (this->has_relative_positions()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *relative_positions_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConfigCpc::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.ConfigCpc)
  GOOGLE_DCHECK_NE(&from, this);
  const ConfigCpc* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ConfigCpc>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.ConfigCpc)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.ConfigCpc)
    MergeFrom(*source);
  }
}

void ConfigCpc::MergeFrom(const ConfigCpc& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.ConfigCpc)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cameras_.MergeFrom(from.cameras_);
  radars_.MergeFrom(from.radars_);
  lidars_.MergeFrom(from.lidars_);
  if (from.has_relative_positions()) {
    mutable_relative_positions()->::esurfing::proto::config::RelativePositions::MergeFrom(from.relative_positions());
  }
}

void ConfigCpc::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.ConfigCpc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConfigCpc::CopyFrom(const ConfigCpc& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.ConfigCpc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigCpc::IsInitialized() const {
  return true;
}

void ConfigCpc::Swap(ConfigCpc* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ConfigCpc::InternalSwap(ConfigCpc* other) {
  using std::swap;
  CastToBase(&cameras_)->InternalSwap(CastToBase(&other->cameras_));
  CastToBase(&radars_)->InternalSwap(CastToBase(&other->radars_));
  CastToBase(&lidars_)->InternalSwap(CastToBase(&other->lidars_));
  swap(relative_positions_, other->relative_positions_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ConfigCpc::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::RelativePose* Arena::CreateMaybeMessage< ::esurfing::proto::config::RelativePose >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::RelativePose >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::RelativePositions* Arena::CreateMaybeMessage< ::esurfing::proto::config::RelativePositions >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::RelativePositions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::ConfigCpc* Arena::CreateMaybeMessage< ::esurfing::proto::config::ConfigCpc >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::ConfigCpc >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)
