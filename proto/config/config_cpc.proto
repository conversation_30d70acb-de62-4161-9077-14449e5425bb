// Copyright
// Author:

syntax = "proto3";

package esurfing.proto.config;

import "proto/math/geo.proto";
import "proto/config/config_camera.proto";
import "proto/config/config_radar.proto";
import "proto/config/config_lidar.proto";

option go_package = "proto/config/config_cpc";
option java_package = "proto.config";
option java_outer_classname = "CpcConfig";
option java_multiple_files = false;

message RelativePose {
  bytes sensor_name = 1;
  math.Transformation3d sensor_tf = 2;
}

message RelativePositions {
  bytes origin_sensor_name = 1;
  repeated RelativePose sensors = 2;
}

message ConfigCpc {
  repeated PinholeCamera cameras = 1;
  repeated Radar radars = 2;
  repeated Lidar lidars = 3;
  RelativePositions relative_positions = 4;
}
