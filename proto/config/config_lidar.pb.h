// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/config/config_lidar.proto

#ifndef PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5flidar_2eproto
#define PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5flidar_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "proto/math/geo.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5flidar_2eproto 

namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[3];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto
namespace esurfing {
namespace proto {
namespace config {
class ConfigLidars;
class ConfigLidarsDefaultTypeInternal;
extern ConfigLidarsDefaultTypeInternal _ConfigLidars_default_instance_;
class Lidar;
class LidarDefaultTypeInternal;
extern LidarDefaultTypeInternal _Lidar_default_instance_;
class LidarIntrisic;
class LidarIntrisicDefaultTypeInternal;
extern LidarIntrisicDefaultTypeInternal _LidarIntrisic_default_instance_;
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> ::esurfing::proto::config::ConfigLidars* Arena::CreateMaybeMessage<::esurfing::proto::config::ConfigLidars>(Arena*);
template<> ::esurfing::proto::config::Lidar* Arena::CreateMaybeMessage<::esurfing::proto::config::Lidar>(Arena*);
template<> ::esurfing::proto::config::LidarIntrisic* Arena::CreateMaybeMessage<::esurfing::proto::config::LidarIntrisic>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace esurfing {
namespace proto {
namespace config {

enum Lidar_Type {
  Lidar_Type_UNKNOWN = 0,
  Lidar_Type_VLP_16 = 1,
  Lidar_Type_VLP_32 = 2,
  Lidar_Type_HESAI_64 = 3,
  Lidar_Type_RS_80 = 4,
  Lidar_Type_RS_128 = 5,
  Lidar_Type_Lidar_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  Lidar_Type_Lidar_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool Lidar_Type_IsValid(int value);
const Lidar_Type Lidar_Type_Type_MIN = Lidar_Type_UNKNOWN;
const Lidar_Type Lidar_Type_Type_MAX = Lidar_Type_RS_128;
const int Lidar_Type_Type_ARRAYSIZE = Lidar_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* Lidar_Type_descriptor();
inline const ::std::string& Lidar_Type_Name(Lidar_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    Lidar_Type_descriptor(), value);
}
inline bool Lidar_Type_Parse(
    const ::std::string& name, Lidar_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Lidar_Type>(
    Lidar_Type_descriptor(), name, value);
}
// ===================================================================

class LidarIntrisic : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.LidarIntrisic) */ {
 public:
  LidarIntrisic();
  virtual ~LidarIntrisic();

  LidarIntrisic(const LidarIntrisic& from);

  inline LidarIntrisic& operator=(const LidarIntrisic& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LidarIntrisic(LidarIntrisic&& from) noexcept
    : LidarIntrisic() {
    *this = ::std::move(from);
  }

  inline LidarIntrisic& operator=(LidarIntrisic&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const LidarIntrisic& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LidarIntrisic* internal_default_instance() {
    return reinterpret_cast<const LidarIntrisic*>(
               &_LidarIntrisic_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(LidarIntrisic* other);
  friend void swap(LidarIntrisic& a, LidarIntrisic& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LidarIntrisic* New() const final {
    return CreateMaybeMessage<LidarIntrisic>(NULL);
  }

  LidarIntrisic* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LidarIntrisic>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LidarIntrisic& from);
  void MergeFrom(const LidarIntrisic& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LidarIntrisic* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bool enabled = 1;
  void clear_enabled();
  static const int kEnabledFieldNumber = 1;
  bool enabled() const;
  void set_enabled(bool value);

  // int32 min_intensity = 2;
  void clear_min_intensity();
  static const int kMinIntensityFieldNumber = 2;
  ::google::protobuf::int32 min_intensity() const;
  void set_min_intensity(::google::protobuf::int32 value);

  // int32 max_intensity = 3;
  void clear_max_intensity();
  static const int kMaxIntensityFieldNumber = 3;
  ::google::protobuf::int32 max_intensity() const;
  void set_max_intensity(::google::protobuf::int32 value);

  // float rot = 4;
  void clear_rot();
  static const int kRotFieldNumber = 4;
  float rot() const;
  void set_rot(float value);

  // float vert = 5;
  void clear_vert();
  static const int kVertFieldNumber = 5;
  float vert() const;
  void set_vert(float value);

  // float dist = 6;
  void clear_dist();
  static const int kDistFieldNumber = 6;
  float dist() const;
  void set_dist(float value);

  // float dist_x = 7;
  void clear_dist_x();
  static const int kDistXFieldNumber = 7;
  float dist_x() const;
  void set_dist_x(float value);

  // float dist_y = 8;
  void clear_dist_y();
  static const int kDistYFieldNumber = 8;
  float dist_y() const;
  void set_dist_y(float value);

  // float vert_offset = 9;
  void clear_vert_offset();
  static const int kVertOffsetFieldNumber = 9;
  float vert_offset() const;
  void set_vert_offset(float value);

  // float horiz_offset = 10;
  void clear_horiz_offset();
  static const int kHorizOffsetFieldNumber = 10;
  float horiz_offset() const;
  void set_horiz_offset(float value);

  // float focal_distance = 11;
  void clear_focal_distance();
  static const int kFocalDistanceFieldNumber = 11;
  float focal_distance() const;
  void set_focal_distance(float value);

  // float focal_scope = 12;
  void clear_focal_scope();
  static const int kFocalScopeFieldNumber = 12;
  float focal_scope() const;
  void set_focal_scope(float value);

  // int32 ring = 13;
  void clear_ring();
  static const int kRingFieldNumber = 13;
  ::google::protobuf::int32 ring() const;
  void set_ring(::google::protobuf::int32 value);

  // float cos_rot = 14;
  void clear_cos_rot();
  static const int kCosRotFieldNumber = 14;
  float cos_rot() const;
  void set_cos_rot(float value);

  // float sin_rot = 15;
  void clear_sin_rot();
  static const int kSinRotFieldNumber = 15;
  float sin_rot() const;
  void set_sin_rot(float value);

  // float cos_vert = 16;
  void clear_cos_vert();
  static const int kCosVertFieldNumber = 16;
  float cos_vert() const;
  void set_cos_vert(float value);

  // float sin_vert = 17;
  void clear_sin_vert();
  static const int kSinVertFieldNumber = 17;
  float sin_vert() const;
  void set_sin_vert(float value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.LidarIntrisic)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  bool enabled_;
  ::google::protobuf::int32 min_intensity_;
  ::google::protobuf::int32 max_intensity_;
  float rot_;
  float vert_;
  float dist_;
  float dist_x_;
  float dist_y_;
  float vert_offset_;
  float horiz_offset_;
  float focal_distance_;
  float focal_scope_;
  ::google::protobuf::int32 ring_;
  float cos_rot_;
  float sin_rot_;
  float cos_vert_;
  float sin_vert_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Lidar : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.Lidar) */ {
 public:
  Lidar();
  virtual ~Lidar();

  Lidar(const Lidar& from);

  inline Lidar& operator=(const Lidar& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Lidar(Lidar&& from) noexcept
    : Lidar() {
    *this = ::std::move(from);
  }

  inline Lidar& operator=(Lidar&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Lidar& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Lidar* internal_default_instance() {
    return reinterpret_cast<const Lidar*>(
               &_Lidar_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(Lidar* other);
  friend void swap(Lidar& a, Lidar& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Lidar* New() const final {
    return CreateMaybeMessage<Lidar>(NULL);
  }

  Lidar* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Lidar>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Lidar& from);
  void MergeFrom(const Lidar& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Lidar* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Lidar_Type Type;
  static const Type UNKNOWN =
    Lidar_Type_UNKNOWN;
  static const Type VLP_16 =
    Lidar_Type_VLP_16;
  static const Type VLP_32 =
    Lidar_Type_VLP_32;
  static const Type HESAI_64 =
    Lidar_Type_HESAI_64;
  static const Type RS_80 =
    Lidar_Type_RS_80;
  static const Type RS_128 =
    Lidar_Type_RS_128;
  static inline bool Type_IsValid(int value) {
    return Lidar_Type_IsValid(value);
  }
  static const Type Type_MIN =
    Lidar_Type_Type_MIN;
  static const Type Type_MAX =
    Lidar_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    Lidar_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return Lidar_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return Lidar_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return Lidar_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.config.LidarIntrisic intrinsics = 3;
  int intrinsics_size() const;
  void clear_intrinsics();
  static const int kIntrinsicsFieldNumber = 3;
  ::esurfing::proto::config::LidarIntrisic* mutable_intrinsics(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::LidarIntrisic >*
      mutable_intrinsics();
  const ::esurfing::proto::config::LidarIntrisic& intrinsics(int index) const;
  ::esurfing::proto::config::LidarIntrisic* add_intrinsics();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::LidarIntrisic >&
      intrinsics() const;

  // bytes name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const void* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // bytes topic = 7;
  void clear_topic();
  static const int kTopicFieldNumber = 7;
  const ::std::string& topic() const;
  void set_topic(const ::std::string& value);
  #if LANG_CXX11
  void set_topic(::std::string&& value);
  #endif
  void set_topic(const char* value);
  void set_topic(const void* value, size_t size);
  ::std::string* mutable_topic();
  ::std::string* release_topic();
  void set_allocated_topic(::std::string* topic);

  // .esurfing.proto.math.Transformation3d tf_vehicle_lidar = 6;
  bool has_tf_vehicle_lidar() const;
  void clear_tf_vehicle_lidar();
  static const int kTfVehicleLidarFieldNumber = 6;
  private:
  const ::esurfing::proto::math::Transformation3d& _internal_tf_vehicle_lidar() const;
  public:
  const ::esurfing::proto::math::Transformation3d& tf_vehicle_lidar() const;
  ::esurfing::proto::math::Transformation3d* release_tf_vehicle_lidar();
  ::esurfing::proto::math::Transformation3d* mutable_tf_vehicle_lidar();
  void set_allocated_tf_vehicle_lidar(::esurfing::proto::math::Transformation3d* tf_vehicle_lidar);

  // .esurfing.proto.config.Lidar.Type type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  ::esurfing::proto::config::Lidar_Type type() const;
  void set_type(::esurfing::proto::config::Lidar_Type value);

  // float min_distance = 4;
  void clear_min_distance();
  static const int kMinDistanceFieldNumber = 4;
  float min_distance() const;
  void set_min_distance(float value);

  // float max_distance = 5;
  void clear_max_distance();
  static const int kMaxDistanceFieldNumber = 5;
  float max_distance() const;
  void set_max_distance(float value);

  // int32 usage = 8;
  void clear_usage();
  static const int kUsageFieldNumber = 8;
  ::google::protobuf::int32 usage() const;
  void set_usage(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.Lidar)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::LidarIntrisic > intrinsics_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr topic_;
  ::esurfing::proto::math::Transformation3d* tf_vehicle_lidar_;
  int type_;
  float min_distance_;
  float max_distance_;
  ::google::protobuf::int32 usage_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ConfigLidars : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.ConfigLidars) */ {
 public:
  ConfigLidars();
  virtual ~ConfigLidars();

  ConfigLidars(const ConfigLidars& from);

  inline ConfigLidars& operator=(const ConfigLidars& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ConfigLidars(ConfigLidars&& from) noexcept
    : ConfigLidars() {
    *this = ::std::move(from);
  }

  inline ConfigLidars& operator=(ConfigLidars&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ConfigLidars& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConfigLidars* internal_default_instance() {
    return reinterpret_cast<const ConfigLidars*>(
               &_ConfigLidars_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(ConfigLidars* other);
  friend void swap(ConfigLidars& a, ConfigLidars& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ConfigLidars* New() const final {
    return CreateMaybeMessage<ConfigLidars>(NULL);
  }

  ConfigLidars* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ConfigLidars>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ConfigLidars& from);
  void MergeFrom(const ConfigLidars& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigLidars* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.config.Lidar lidars = 1;
  int lidars_size() const;
  void clear_lidars();
  static const int kLidarsFieldNumber = 1;
  ::esurfing::proto::config::Lidar* mutable_lidars(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >*
      mutable_lidars();
  const ::esurfing::proto::config::Lidar& lidars(int index) const;
  ::esurfing::proto::config::Lidar* add_lidars();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >&
      lidars() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.ConfigLidars)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar > lidars_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LidarIntrisic

// bool enabled = 1;
inline void LidarIntrisic::clear_enabled() {
  enabled_ = false;
}
inline bool LidarIntrisic::enabled() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.enabled)
  return enabled_;
}
inline void LidarIntrisic::set_enabled(bool value) {
  
  enabled_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.enabled)
}

// int32 min_intensity = 2;
inline void LidarIntrisic::clear_min_intensity() {
  min_intensity_ = 0;
}
inline ::google::protobuf::int32 LidarIntrisic::min_intensity() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.min_intensity)
  return min_intensity_;
}
inline void LidarIntrisic::set_min_intensity(::google::protobuf::int32 value) {
  
  min_intensity_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.min_intensity)
}

// int32 max_intensity = 3;
inline void LidarIntrisic::clear_max_intensity() {
  max_intensity_ = 0;
}
inline ::google::protobuf::int32 LidarIntrisic::max_intensity() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.max_intensity)
  return max_intensity_;
}
inline void LidarIntrisic::set_max_intensity(::google::protobuf::int32 value) {
  
  max_intensity_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.max_intensity)
}

// float rot = 4;
inline void LidarIntrisic::clear_rot() {
  rot_ = 0;
}
inline float LidarIntrisic::rot() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.rot)
  return rot_;
}
inline void LidarIntrisic::set_rot(float value) {
  
  rot_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.rot)
}

// float vert = 5;
inline void LidarIntrisic::clear_vert() {
  vert_ = 0;
}
inline float LidarIntrisic::vert() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.vert)
  return vert_;
}
inline void LidarIntrisic::set_vert(float value) {
  
  vert_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.vert)
}

// float dist = 6;
inline void LidarIntrisic::clear_dist() {
  dist_ = 0;
}
inline float LidarIntrisic::dist() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.dist)
  return dist_;
}
inline void LidarIntrisic::set_dist(float value) {
  
  dist_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.dist)
}

// float dist_x = 7;
inline void LidarIntrisic::clear_dist_x() {
  dist_x_ = 0;
}
inline float LidarIntrisic::dist_x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.dist_x)
  return dist_x_;
}
inline void LidarIntrisic::set_dist_x(float value) {
  
  dist_x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.dist_x)
}

// float dist_y = 8;
inline void LidarIntrisic::clear_dist_y() {
  dist_y_ = 0;
}
inline float LidarIntrisic::dist_y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.dist_y)
  return dist_y_;
}
inline void LidarIntrisic::set_dist_y(float value) {
  
  dist_y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.dist_y)
}

// float vert_offset = 9;
inline void LidarIntrisic::clear_vert_offset() {
  vert_offset_ = 0;
}
inline float LidarIntrisic::vert_offset() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.vert_offset)
  return vert_offset_;
}
inline void LidarIntrisic::set_vert_offset(float value) {
  
  vert_offset_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.vert_offset)
}

// float horiz_offset = 10;
inline void LidarIntrisic::clear_horiz_offset() {
  horiz_offset_ = 0;
}
inline float LidarIntrisic::horiz_offset() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.horiz_offset)
  return horiz_offset_;
}
inline void LidarIntrisic::set_horiz_offset(float value) {
  
  horiz_offset_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.horiz_offset)
}

// float focal_distance = 11;
inline void LidarIntrisic::clear_focal_distance() {
  focal_distance_ = 0;
}
inline float LidarIntrisic::focal_distance() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.focal_distance)
  return focal_distance_;
}
inline void LidarIntrisic::set_focal_distance(float value) {
  
  focal_distance_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.focal_distance)
}

// float focal_scope = 12;
inline void LidarIntrisic::clear_focal_scope() {
  focal_scope_ = 0;
}
inline float LidarIntrisic::focal_scope() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.focal_scope)
  return focal_scope_;
}
inline void LidarIntrisic::set_focal_scope(float value) {
  
  focal_scope_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.focal_scope)
}

// int32 ring = 13;
inline void LidarIntrisic::clear_ring() {
  ring_ = 0;
}
inline ::google::protobuf::int32 LidarIntrisic::ring() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.ring)
  return ring_;
}
inline void LidarIntrisic::set_ring(::google::protobuf::int32 value) {
  
  ring_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.ring)
}

// float cos_rot = 14;
inline void LidarIntrisic::clear_cos_rot() {
  cos_rot_ = 0;
}
inline float LidarIntrisic::cos_rot() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.cos_rot)
  return cos_rot_;
}
inline void LidarIntrisic::set_cos_rot(float value) {
  
  cos_rot_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.cos_rot)
}

// float sin_rot = 15;
inline void LidarIntrisic::clear_sin_rot() {
  sin_rot_ = 0;
}
inline float LidarIntrisic::sin_rot() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.sin_rot)
  return sin_rot_;
}
inline void LidarIntrisic::set_sin_rot(float value) {
  
  sin_rot_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.sin_rot)
}

// float cos_vert = 16;
inline void LidarIntrisic::clear_cos_vert() {
  cos_vert_ = 0;
}
inline float LidarIntrisic::cos_vert() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.cos_vert)
  return cos_vert_;
}
inline void LidarIntrisic::set_cos_vert(float value) {
  
  cos_vert_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.cos_vert)
}

// float sin_vert = 17;
inline void LidarIntrisic::clear_sin_vert() {
  sin_vert_ = 0;
}
inline float LidarIntrisic::sin_vert() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.LidarIntrisic.sin_vert)
  return sin_vert_;
}
inline void LidarIntrisic::set_sin_vert(float value) {
  
  sin_vert_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.LidarIntrisic.sin_vert)
}

// -------------------------------------------------------------------

// Lidar

// bytes name = 1;
inline void Lidar::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Lidar::name() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.name)
  return name_.GetNoArena();
}
inline void Lidar::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Lidar.name)
}
#if LANG_CXX11
inline void Lidar::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.Lidar.name)
}
#endif
inline void Lidar::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.Lidar.name)
}
inline void Lidar::set_name(const void* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.Lidar.name)
}
inline ::std::string* Lidar::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Lidar.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Lidar::release_name() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.Lidar.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Lidar::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.Lidar.name)
}

// .esurfing.proto.config.Lidar.Type type = 2;
inline void Lidar::clear_type() {
  type_ = 0;
}
inline ::esurfing::proto::config::Lidar_Type Lidar::type() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.type)
  return static_cast< ::esurfing::proto::config::Lidar_Type >(type_);
}
inline void Lidar::set_type(::esurfing::proto::config::Lidar_Type value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Lidar.type)
}

// repeated .esurfing.proto.config.LidarIntrisic intrinsics = 3;
inline int Lidar::intrinsics_size() const {
  return intrinsics_.size();
}
inline void Lidar::clear_intrinsics() {
  intrinsics_.Clear();
}
inline ::esurfing::proto::config::LidarIntrisic* Lidar::mutable_intrinsics(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Lidar.intrinsics)
  return intrinsics_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::LidarIntrisic >*
Lidar::mutable_intrinsics() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.Lidar.intrinsics)
  return &intrinsics_;
}
inline const ::esurfing::proto::config::LidarIntrisic& Lidar::intrinsics(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.intrinsics)
  return intrinsics_.Get(index);
}
inline ::esurfing::proto::config::LidarIntrisic* Lidar::add_intrinsics() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.Lidar.intrinsics)
  return intrinsics_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::LidarIntrisic >&
Lidar::intrinsics() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.Lidar.intrinsics)
  return intrinsics_;
}

// float min_distance = 4;
inline void Lidar::clear_min_distance() {
  min_distance_ = 0;
}
inline float Lidar::min_distance() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.min_distance)
  return min_distance_;
}
inline void Lidar::set_min_distance(float value) {
  
  min_distance_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Lidar.min_distance)
}

// float max_distance = 5;
inline void Lidar::clear_max_distance() {
  max_distance_ = 0;
}
inline float Lidar::max_distance() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.max_distance)
  return max_distance_;
}
inline void Lidar::set_max_distance(float value) {
  
  max_distance_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Lidar.max_distance)
}

// .esurfing.proto.math.Transformation3d tf_vehicle_lidar = 6;
inline bool Lidar::has_tf_vehicle_lidar() const {
  return this != internal_default_instance() && tf_vehicle_lidar_ != NULL;
}
inline const ::esurfing::proto::math::Transformation3d& Lidar::_internal_tf_vehicle_lidar() const {
  return *tf_vehicle_lidar_;
}
inline const ::esurfing::proto::math::Transformation3d& Lidar::tf_vehicle_lidar() const {
  const ::esurfing::proto::math::Transformation3d* p = tf_vehicle_lidar_;
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.tf_vehicle_lidar)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Transformation3d*>(
      &::esurfing::proto::math::_Transformation3d_default_instance_);
}
inline ::esurfing::proto::math::Transformation3d* Lidar::release_tf_vehicle_lidar() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.Lidar.tf_vehicle_lidar)
  
  ::esurfing::proto::math::Transformation3d* temp = tf_vehicle_lidar_;
  tf_vehicle_lidar_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Transformation3d* Lidar::mutable_tf_vehicle_lidar() {
  
  if (tf_vehicle_lidar_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Transformation3d>(GetArenaNoVirtual());
    tf_vehicle_lidar_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Lidar.tf_vehicle_lidar)
  return tf_vehicle_lidar_;
}
inline void Lidar::set_allocated_tf_vehicle_lidar(::esurfing::proto::math::Transformation3d* tf_vehicle_lidar) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tf_vehicle_lidar_);
  }
  if (tf_vehicle_lidar) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      tf_vehicle_lidar = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tf_vehicle_lidar, submessage_arena);
    }
    
  } else {
    
  }
  tf_vehicle_lidar_ = tf_vehicle_lidar;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.Lidar.tf_vehicle_lidar)
}

// bytes topic = 7;
inline void Lidar::clear_topic() {
  topic_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Lidar::topic() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.topic)
  return topic_.GetNoArena();
}
inline void Lidar::set_topic(const ::std::string& value) {
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Lidar.topic)
}
#if LANG_CXX11
inline void Lidar::set_topic(::std::string&& value) {
  
  topic_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.Lidar.topic)
}
#endif
inline void Lidar::set_topic(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.Lidar.topic)
}
inline void Lidar::set_topic(const void* value, size_t size) {
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.Lidar.topic)
}
inline ::std::string* Lidar::mutable_topic() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Lidar.topic)
  return topic_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Lidar::release_topic() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.Lidar.topic)
  
  return topic_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Lidar::set_allocated_topic(::std::string* topic) {
  if (topic != NULL) {
    
  } else {
    
  }
  topic_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), topic);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.Lidar.topic)
}

// int32 usage = 8;
inline void Lidar::clear_usage() {
  usage_ = 0;
}
inline ::google::protobuf::int32 Lidar::usage() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Lidar.usage)
  return usage_;
}
inline void Lidar::set_usage(::google::protobuf::int32 value) {
  
  usage_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Lidar.usage)
}

// -------------------------------------------------------------------

// ConfigLidars

// repeated .esurfing.proto.config.Lidar lidars = 1;
inline int ConfigLidars::lidars_size() const {
  return lidars_.size();
}
inline void ConfigLidars::clear_lidars() {
  lidars_.Clear();
}
inline ::esurfing::proto::config::Lidar* ConfigLidars::mutable_lidars(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.ConfigLidars.lidars)
  return lidars_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >*
ConfigLidars::mutable_lidars() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.ConfigLidars.lidars)
  return &lidars_;
}
inline const ::esurfing::proto::config::Lidar& ConfigLidars::lidars(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.ConfigLidars.lidars)
  return lidars_.Get(index);
}
inline ::esurfing::proto::config::Lidar* ConfigLidars::add_lidars() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.ConfigLidars.lidars)
  return lidars_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >&
ConfigLidars::lidars() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.ConfigLidars.lidars)
  return lidars_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace config
}  // namespace proto
}  // namespace esurfing

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::esurfing::proto::config::Lidar_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::esurfing::proto::config::Lidar_Type>() {
  return ::esurfing::proto::config::Lidar_Type_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5flidar_2eproto
