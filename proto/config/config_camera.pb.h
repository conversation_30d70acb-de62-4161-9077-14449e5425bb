// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/config/config_camera.proto

#ifndef PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fcamera_2eproto
#define PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fcamera_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "proto/math/geo.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto 

namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[2];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto
namespace esurfing {
namespace proto {
namespace config {
class ConfigCameras;
class ConfigCamerasDefaultTypeInternal;
extern ConfigCamerasDefaultTypeInternal _ConfigCameras_default_instance_;
class PinholeCamera;
class PinholeCameraDefaultTypeInternal;
extern PinholeCameraDefaultTypeInternal _PinholeCamera_default_instance_;
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> ::esurfing::proto::config::ConfigCameras* Arena::CreateMaybeMessage<::esurfing::proto::config::ConfigCameras>(Arena*);
template<> ::esurfing::proto::config::PinholeCamera* Arena::CreateMaybeMessage<::esurfing::proto::config::PinholeCamera>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace esurfing {
namespace proto {
namespace config {

enum PinholeCamera_Usage {
  PinholeCamera_Usage_UNKNOWN = 0,
  PinholeCamera_Usage_VISION = 1,
  PinholeCamera_Usage_SIGNAL = 2,
  PinholeCamera_Usage_ALL = 3,
  PinholeCamera_Usage_PinholeCamera_Usage_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  PinholeCamera_Usage_PinholeCamera_Usage_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool PinholeCamera_Usage_IsValid(int value);
const PinholeCamera_Usage PinholeCamera_Usage_Usage_MIN = PinholeCamera_Usage_UNKNOWN;
const PinholeCamera_Usage PinholeCamera_Usage_Usage_MAX = PinholeCamera_Usage_ALL;
const int PinholeCamera_Usage_Usage_ARRAYSIZE = PinholeCamera_Usage_Usage_MAX + 1;

const ::google::protobuf::EnumDescriptor* PinholeCamera_Usage_descriptor();
inline const ::std::string& PinholeCamera_Usage_Name(PinholeCamera_Usage value) {
  return ::google::protobuf::internal::NameOfEnum(
    PinholeCamera_Usage_descriptor(), value);
}
inline bool PinholeCamera_Usage_Parse(
    const ::std::string& name, PinholeCamera_Usage* value) {
  return ::google::protobuf::internal::ParseNamedEnum<PinholeCamera_Usage>(
    PinholeCamera_Usage_descriptor(), name, value);
}
// ===================================================================

class PinholeCamera : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.PinholeCamera) */ {
 public:
  PinholeCamera();
  virtual ~PinholeCamera();

  PinholeCamera(const PinholeCamera& from);

  inline PinholeCamera& operator=(const PinholeCamera& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PinholeCamera(PinholeCamera&& from) noexcept
    : PinholeCamera() {
    *this = ::std::move(from);
  }

  inline PinholeCamera& operator=(PinholeCamera&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const PinholeCamera& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PinholeCamera* internal_default_instance() {
    return reinterpret_cast<const PinholeCamera*>(
               &_PinholeCamera_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(PinholeCamera* other);
  friend void swap(PinholeCamera& a, PinholeCamera& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PinholeCamera* New() const final {
    return CreateMaybeMessage<PinholeCamera>(NULL);
  }

  PinholeCamera* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PinholeCamera>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PinholeCamera& from);
  void MergeFrom(const PinholeCamera& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PinholeCamera* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef PinholeCamera_Usage Usage;
  static const Usage UNKNOWN =
    PinholeCamera_Usage_UNKNOWN;
  static const Usage VISION =
    PinholeCamera_Usage_VISION;
  static const Usage SIGNAL =
    PinholeCamera_Usage_SIGNAL;
  static const Usage ALL =
    PinholeCamera_Usage_ALL;
  static inline bool Usage_IsValid(int value) {
    return PinholeCamera_Usage_IsValid(value);
  }
  static const Usage Usage_MIN =
    PinholeCamera_Usage_Usage_MIN;
  static const Usage Usage_MAX =
    PinholeCamera_Usage_Usage_MAX;
  static const int Usage_ARRAYSIZE =
    PinholeCamera_Usage_Usage_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Usage_descriptor() {
    return PinholeCamera_Usage_descriptor();
  }
  static inline const ::std::string& Usage_Name(Usage value) {
    return PinholeCamera_Usage_Name(value);
  }
  static inline bool Usage_Parse(const ::std::string& name,
      Usage* value) {
    return PinholeCamera_Usage_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated double radial_distortion = 10;
  int radial_distortion_size() const;
  void clear_radial_distortion();
  static const int kRadialDistortionFieldNumber = 10;
  double radial_distortion(int index) const;
  void set_radial_distortion(int index, double value);
  void add_radial_distortion(double value);
  const ::google::protobuf::RepeatedField< double >&
      radial_distortion() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_radial_distortion();

  // bytes name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const void* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // bytes topic = 13;
  void clear_topic();
  static const int kTopicFieldNumber = 13;
  const ::std::string& topic() const;
  void set_topic(const ::std::string& value);
  #if LANG_CXX11
  void set_topic(::std::string&& value);
  #endif
  void set_topic(const char* value);
  void set_topic(const void* value, size_t size);
  ::std::string* mutable_topic();
  ::std::string* release_topic();
  void set_allocated_topic(::std::string* topic);

  // .esurfing.proto.math.Transformation3d tf_vehicle_camera = 5;
  bool has_tf_vehicle_camera() const;
  void clear_tf_vehicle_camera();
  static const int kTfVehicleCameraFieldNumber = 5;
  private:
  const ::esurfing::proto::math::Transformation3d& _internal_tf_vehicle_camera() const;
  public:
  const ::esurfing::proto::math::Transformation3d& tf_vehicle_camera() const;
  ::esurfing::proto::math::Transformation3d* release_tf_vehicle_camera();
  ::esurfing::proto::math::Transformation3d* mutable_tf_vehicle_camera();
  void set_allocated_tf_vehicle_camera(::esurfing::proto::math::Transformation3d* tf_vehicle_camera);

  // int32 image_width = 2;
  void clear_image_width();
  static const int kImageWidthFieldNumber = 2;
  ::google::protobuf::int32 image_width() const;
  void set_image_width(::google::protobuf::int32 value);

  // int32 image_height = 3;
  void clear_image_height();
  static const int kImageHeightFieldNumber = 3;
  ::google::protobuf::int32 image_height() const;
  void set_image_height(::google::protobuf::int32 value);

  // double frame_rate = 4;
  void clear_frame_rate();
  static const int kFrameRateFieldNumber = 4;
  double frame_rate() const;
  void set_frame_rate(double value);

  // double fx = 6;
  void clear_fx();
  static const int kFxFieldNumber = 6;
  double fx() const;
  void set_fx(double value);

  // double fy = 7;
  void clear_fy();
  static const int kFyFieldNumber = 7;
  double fy() const;
  void set_fy(double value);

  // double cx = 8;
  void clear_cx();
  static const int kCxFieldNumber = 8;
  double cx() const;
  void set_cx(double value);

  // double cy = 9;
  void clear_cy();
  static const int kCyFieldNumber = 9;
  double cy() const;
  void set_cy(double value);

  // bool flip = 11;
  void clear_flip();
  static const int kFlipFieldNumber = 11;
  bool flip() const;
  void set_flip(bool value);

  // bool crop = 12;
  void clear_crop();
  static const int kCropFieldNumber = 12;
  bool crop() const;
  void set_crop(bool value);

  // bool time_synced = 14;
  void clear_time_synced();
  static const int kTimeSyncedFieldNumber = 14;
  bool time_synced() const;
  void set_time_synced(bool value);

  // int32 usage = 15;
  void clear_usage();
  static const int kUsageFieldNumber = 15;
  ::google::protobuf::int32 usage() const;
  void set_usage(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.PinholeCamera)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< double > radial_distortion_;
  mutable int _radial_distortion_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr topic_;
  ::esurfing::proto::math::Transformation3d* tf_vehicle_camera_;
  ::google::protobuf::int32 image_width_;
  ::google::protobuf::int32 image_height_;
  double frame_rate_;
  double fx_;
  double fy_;
  double cx_;
  double cy_;
  bool flip_;
  bool crop_;
  bool time_synced_;
  ::google::protobuf::int32 usage_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ConfigCameras : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.ConfigCameras) */ {
 public:
  ConfigCameras();
  virtual ~ConfigCameras();

  ConfigCameras(const ConfigCameras& from);

  inline ConfigCameras& operator=(const ConfigCameras& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ConfigCameras(ConfigCameras&& from) noexcept
    : ConfigCameras() {
    *this = ::std::move(from);
  }

  inline ConfigCameras& operator=(ConfigCameras&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ConfigCameras& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConfigCameras* internal_default_instance() {
    return reinterpret_cast<const ConfigCameras*>(
               &_ConfigCameras_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(ConfigCameras* other);
  friend void swap(ConfigCameras& a, ConfigCameras& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ConfigCameras* New() const final {
    return CreateMaybeMessage<ConfigCameras>(NULL);
  }

  ConfigCameras* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ConfigCameras>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ConfigCameras& from);
  void MergeFrom(const ConfigCameras& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigCameras* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  int cameras_size() const;
  void clear_cameras();
  static const int kCamerasFieldNumber = 1;
  ::esurfing::proto::config::PinholeCamera* mutable_cameras(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >*
      mutable_cameras();
  const ::esurfing::proto::config::PinholeCamera& cameras(int index) const;
  ::esurfing::proto::config::PinholeCamera* add_cameras();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >&
      cameras() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.ConfigCameras)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera > cameras_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fcamera_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PinholeCamera

// bytes name = 1;
inline void PinholeCamera::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PinholeCamera::name() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.name)
  return name_.GetNoArena();
}
inline void PinholeCamera::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.name)
}
#if LANG_CXX11
inline void PinholeCamera::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.PinholeCamera.name)
}
#endif
inline void PinholeCamera::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.PinholeCamera.name)
}
inline void PinholeCamera::set_name(const void* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.PinholeCamera.name)
}
inline ::std::string* PinholeCamera::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.PinholeCamera.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PinholeCamera::release_name() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.PinholeCamera.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PinholeCamera::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.PinholeCamera.name)
}

// int32 image_width = 2;
inline void PinholeCamera::clear_image_width() {
  image_width_ = 0;
}
inline ::google::protobuf::int32 PinholeCamera::image_width() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.image_width)
  return image_width_;
}
inline void PinholeCamera::set_image_width(::google::protobuf::int32 value) {
  
  image_width_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.image_width)
}

// int32 image_height = 3;
inline void PinholeCamera::clear_image_height() {
  image_height_ = 0;
}
inline ::google::protobuf::int32 PinholeCamera::image_height() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.image_height)
  return image_height_;
}
inline void PinholeCamera::set_image_height(::google::protobuf::int32 value) {
  
  image_height_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.image_height)
}

// double frame_rate = 4;
inline void PinholeCamera::clear_frame_rate() {
  frame_rate_ = 0;
}
inline double PinholeCamera::frame_rate() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.frame_rate)
  return frame_rate_;
}
inline void PinholeCamera::set_frame_rate(double value) {
  
  frame_rate_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.frame_rate)
}

// .esurfing.proto.math.Transformation3d tf_vehicle_camera = 5;
inline bool PinholeCamera::has_tf_vehicle_camera() const {
  return this != internal_default_instance() && tf_vehicle_camera_ != NULL;
}
inline const ::esurfing::proto::math::Transformation3d& PinholeCamera::_internal_tf_vehicle_camera() const {
  return *tf_vehicle_camera_;
}
inline const ::esurfing::proto::math::Transformation3d& PinholeCamera::tf_vehicle_camera() const {
  const ::esurfing::proto::math::Transformation3d* p = tf_vehicle_camera_;
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.tf_vehicle_camera)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Transformation3d*>(
      &::esurfing::proto::math::_Transformation3d_default_instance_);
}
inline ::esurfing::proto::math::Transformation3d* PinholeCamera::release_tf_vehicle_camera() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.PinholeCamera.tf_vehicle_camera)
  
  ::esurfing::proto::math::Transformation3d* temp = tf_vehicle_camera_;
  tf_vehicle_camera_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Transformation3d* PinholeCamera::mutable_tf_vehicle_camera() {
  
  if (tf_vehicle_camera_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Transformation3d>(GetArenaNoVirtual());
    tf_vehicle_camera_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.PinholeCamera.tf_vehicle_camera)
  return tf_vehicle_camera_;
}
inline void PinholeCamera::set_allocated_tf_vehicle_camera(::esurfing::proto::math::Transformation3d* tf_vehicle_camera) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tf_vehicle_camera_);
  }
  if (tf_vehicle_camera) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      tf_vehicle_camera = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tf_vehicle_camera, submessage_arena);
    }
    
  } else {
    
  }
  tf_vehicle_camera_ = tf_vehicle_camera;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.PinholeCamera.tf_vehicle_camera)
}

// double fx = 6;
inline void PinholeCamera::clear_fx() {
  fx_ = 0;
}
inline double PinholeCamera::fx() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.fx)
  return fx_;
}
inline void PinholeCamera::set_fx(double value) {
  
  fx_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.fx)
}

// double fy = 7;
inline void PinholeCamera::clear_fy() {
  fy_ = 0;
}
inline double PinholeCamera::fy() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.fy)
  return fy_;
}
inline void PinholeCamera::set_fy(double value) {
  
  fy_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.fy)
}

// double cx = 8;
inline void PinholeCamera::clear_cx() {
  cx_ = 0;
}
inline double PinholeCamera::cx() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.cx)
  return cx_;
}
inline void PinholeCamera::set_cx(double value) {
  
  cx_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.cx)
}

// double cy = 9;
inline void PinholeCamera::clear_cy() {
  cy_ = 0;
}
inline double PinholeCamera::cy() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.cy)
  return cy_;
}
inline void PinholeCamera::set_cy(double value) {
  
  cy_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.cy)
}

// repeated double radial_distortion = 10;
inline int PinholeCamera::radial_distortion_size() const {
  return radial_distortion_.size();
}
inline void PinholeCamera::clear_radial_distortion() {
  radial_distortion_.Clear();
}
inline double PinholeCamera::radial_distortion(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.radial_distortion)
  return radial_distortion_.Get(index);
}
inline void PinholeCamera::set_radial_distortion(int index, double value) {
  radial_distortion_.Set(index, value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.radial_distortion)
}
inline void PinholeCamera::add_radial_distortion(double value) {
  radial_distortion_.Add(value);
  // @@protoc_insertion_point(field_add:esurfing.proto.config.PinholeCamera.radial_distortion)
}
inline const ::google::protobuf::RepeatedField< double >&
PinholeCamera::radial_distortion() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.PinholeCamera.radial_distortion)
  return radial_distortion_;
}
inline ::google::protobuf::RepeatedField< double >*
PinholeCamera::mutable_radial_distortion() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.PinholeCamera.radial_distortion)
  return &radial_distortion_;
}

// bool flip = 11;
inline void PinholeCamera::clear_flip() {
  flip_ = false;
}
inline bool PinholeCamera::flip() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.flip)
  return flip_;
}
inline void PinholeCamera::set_flip(bool value) {
  
  flip_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.flip)
}

// bool crop = 12;
inline void PinholeCamera::clear_crop() {
  crop_ = false;
}
inline bool PinholeCamera::crop() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.crop)
  return crop_;
}
inline void PinholeCamera::set_crop(bool value) {
  
  crop_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.crop)
}

// bytes topic = 13;
inline void PinholeCamera::clear_topic() {
  topic_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PinholeCamera::topic() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.topic)
  return topic_.GetNoArena();
}
inline void PinholeCamera::set_topic(const ::std::string& value) {
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.topic)
}
#if LANG_CXX11
inline void PinholeCamera::set_topic(::std::string&& value) {
  
  topic_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.PinholeCamera.topic)
}
#endif
inline void PinholeCamera::set_topic(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.PinholeCamera.topic)
}
inline void PinholeCamera::set_topic(const void* value, size_t size) {
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.PinholeCamera.topic)
}
inline ::std::string* PinholeCamera::mutable_topic() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.PinholeCamera.topic)
  return topic_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PinholeCamera::release_topic() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.PinholeCamera.topic)
  
  return topic_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PinholeCamera::set_allocated_topic(::std::string* topic) {
  if (topic != NULL) {
    
  } else {
    
  }
  topic_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), topic);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.PinholeCamera.topic)
}

// bool time_synced = 14;
inline void PinholeCamera::clear_time_synced() {
  time_synced_ = false;
}
inline bool PinholeCamera::time_synced() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.time_synced)
  return time_synced_;
}
inline void PinholeCamera::set_time_synced(bool value) {
  
  time_synced_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.time_synced)
}

// int32 usage = 15;
inline void PinholeCamera::clear_usage() {
  usage_ = 0;
}
inline ::google::protobuf::int32 PinholeCamera::usage() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.PinholeCamera.usage)
  return usage_;
}
inline void PinholeCamera::set_usage(::google::protobuf::int32 value) {
  
  usage_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.PinholeCamera.usage)
}

// -------------------------------------------------------------------

// ConfigCameras

// repeated .esurfing.proto.config.PinholeCamera cameras = 1;
inline int ConfigCameras::cameras_size() const {
  return cameras_.size();
}
inline void ConfigCameras::clear_cameras() {
  cameras_.Clear();
}
inline ::esurfing::proto::config::PinholeCamera* ConfigCameras::mutable_cameras(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.ConfigCameras.cameras)
  return cameras_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >*
ConfigCameras::mutable_cameras() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.ConfigCameras.cameras)
  return &cameras_;
}
inline const ::esurfing::proto::config::PinholeCamera& ConfigCameras::cameras(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.ConfigCameras.cameras)
  return cameras_.Get(index);
}
inline ::esurfing::proto::config::PinholeCamera* ConfigCameras::add_cameras() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.ConfigCameras.cameras)
  return cameras_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >&
ConfigCameras::cameras() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.ConfigCameras.cameras)
  return cameras_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace config
}  // namespace proto
}  // namespace esurfing

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::esurfing::proto::config::PinholeCamera_Usage> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::esurfing::proto::config::PinholeCamera_Usage>() {
  return ::esurfing::proto::config::PinholeCamera_Usage_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fcamera_2eproto
