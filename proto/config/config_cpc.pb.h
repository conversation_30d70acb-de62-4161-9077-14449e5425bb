// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/config/config_cpc.proto

#ifndef PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fcpc_2eproto
#define PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fcpc_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/math/geo.pb.h"
#include "proto/config/config_camera.pb.h"
#include "proto/config/config_radar.pb.h"
#include "proto/config/config_lidar.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto 

namespace protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[3];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto
namespace esurfing {
namespace proto {
namespace config {
class ConfigCpc;
class ConfigCpcDefaultTypeInternal;
extern ConfigCpcDefaultTypeInternal _ConfigCpc_default_instance_;
class RelativePose;
class RelativePoseDefaultTypeInternal;
extern RelativePoseDefaultTypeInternal _RelativePose_default_instance_;
class RelativePositions;
class RelativePositionsDefaultTypeInternal;
extern RelativePositionsDefaultTypeInternal _RelativePositions_default_instance_;
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> ::esurfing::proto::config::ConfigCpc* Arena::CreateMaybeMessage<::esurfing::proto::config::ConfigCpc>(Arena*);
template<> ::esurfing::proto::config::RelativePose* Arena::CreateMaybeMessage<::esurfing::proto::config::RelativePose>(Arena*);
template<> ::esurfing::proto::config::RelativePositions* Arena::CreateMaybeMessage<::esurfing::proto::config::RelativePositions>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace esurfing {
namespace proto {
namespace config {

// ===================================================================

class RelativePose : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.RelativePose) */ {
 public:
  RelativePose();
  virtual ~RelativePose();

  RelativePose(const RelativePose& from);

  inline RelativePose& operator=(const RelativePose& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RelativePose(RelativePose&& from) noexcept
    : RelativePose() {
    *this = ::std::move(from);
  }

  inline RelativePose& operator=(RelativePose&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RelativePose& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RelativePose* internal_default_instance() {
    return reinterpret_cast<const RelativePose*>(
               &_RelativePose_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(RelativePose* other);
  friend void swap(RelativePose& a, RelativePose& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RelativePose* New() const final {
    return CreateMaybeMessage<RelativePose>(NULL);
  }

  RelativePose* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RelativePose>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RelativePose& from);
  void MergeFrom(const RelativePose& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RelativePose* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes sensor_name = 1;
  void clear_sensor_name();
  static const int kSensorNameFieldNumber = 1;
  const ::std::string& sensor_name() const;
  void set_sensor_name(const ::std::string& value);
  #if LANG_CXX11
  void set_sensor_name(::std::string&& value);
  #endif
  void set_sensor_name(const char* value);
  void set_sensor_name(const void* value, size_t size);
  ::std::string* mutable_sensor_name();
  ::std::string* release_sensor_name();
  void set_allocated_sensor_name(::std::string* sensor_name);

  // .esurfing.proto.math.Transformation3d sensor_tf = 2;
  bool has_sensor_tf() const;
  void clear_sensor_tf();
  static const int kSensorTfFieldNumber = 2;
  private:
  const ::esurfing::proto::math::Transformation3d& _internal_sensor_tf() const;
  public:
  const ::esurfing::proto::math::Transformation3d& sensor_tf() const;
  ::esurfing::proto::math::Transformation3d* release_sensor_tf();
  ::esurfing::proto::math::Transformation3d* mutable_sensor_tf();
  void set_allocated_sensor_tf(::esurfing::proto::math::Transformation3d* sensor_tf);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.RelativePose)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr sensor_name_;
  ::esurfing::proto::math::Transformation3d* sensor_tf_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RelativePositions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.RelativePositions) */ {
 public:
  RelativePositions();
  virtual ~RelativePositions();

  RelativePositions(const RelativePositions& from);

  inline RelativePositions& operator=(const RelativePositions& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RelativePositions(RelativePositions&& from) noexcept
    : RelativePositions() {
    *this = ::std::move(from);
  }

  inline RelativePositions& operator=(RelativePositions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RelativePositions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RelativePositions* internal_default_instance() {
    return reinterpret_cast<const RelativePositions*>(
               &_RelativePositions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(RelativePositions* other);
  friend void swap(RelativePositions& a, RelativePositions& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RelativePositions* New() const final {
    return CreateMaybeMessage<RelativePositions>(NULL);
  }

  RelativePositions* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RelativePositions>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RelativePositions& from);
  void MergeFrom(const RelativePositions& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RelativePositions* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.config.RelativePose sensors = 2;
  int sensors_size() const;
  void clear_sensors();
  static const int kSensorsFieldNumber = 2;
  ::esurfing::proto::config::RelativePose* mutable_sensors(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RelativePose >*
      mutable_sensors();
  const ::esurfing::proto::config::RelativePose& sensors(int index) const;
  ::esurfing::proto::config::RelativePose* add_sensors();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RelativePose >&
      sensors() const;

  // bytes origin_sensor_name = 1;
  void clear_origin_sensor_name();
  static const int kOriginSensorNameFieldNumber = 1;
  const ::std::string& origin_sensor_name() const;
  void set_origin_sensor_name(const ::std::string& value);
  #if LANG_CXX11
  void set_origin_sensor_name(::std::string&& value);
  #endif
  void set_origin_sensor_name(const char* value);
  void set_origin_sensor_name(const void* value, size_t size);
  ::std::string* mutable_origin_sensor_name();
  ::std::string* release_origin_sensor_name();
  void set_allocated_origin_sensor_name(::std::string* origin_sensor_name);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.RelativePositions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RelativePose > sensors_;
  ::google::protobuf::internal::ArenaStringPtr origin_sensor_name_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ConfigCpc : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.ConfigCpc) */ {
 public:
  ConfigCpc();
  virtual ~ConfigCpc();

  ConfigCpc(const ConfigCpc& from);

  inline ConfigCpc& operator=(const ConfigCpc& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ConfigCpc(ConfigCpc&& from) noexcept
    : ConfigCpc() {
    *this = ::std::move(from);
  }

  inline ConfigCpc& operator=(ConfigCpc&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ConfigCpc& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConfigCpc* internal_default_instance() {
    return reinterpret_cast<const ConfigCpc*>(
               &_ConfigCpc_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(ConfigCpc* other);
  friend void swap(ConfigCpc& a, ConfigCpc& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ConfigCpc* New() const final {
    return CreateMaybeMessage<ConfigCpc>(NULL);
  }

  ConfigCpc* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ConfigCpc>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ConfigCpc& from);
  void MergeFrom(const ConfigCpc& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigCpc* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.config.PinholeCamera cameras = 1;
  int cameras_size() const;
  void clear_cameras();
  static const int kCamerasFieldNumber = 1;
  ::esurfing::proto::config::PinholeCamera* mutable_cameras(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >*
      mutable_cameras();
  const ::esurfing::proto::config::PinholeCamera& cameras(int index) const;
  ::esurfing::proto::config::PinholeCamera* add_cameras();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >&
      cameras() const;

  // repeated .esurfing.proto.config.Radar radars = 2;
  int radars_size() const;
  void clear_radars();
  static const int kRadarsFieldNumber = 2;
  ::esurfing::proto::config::Radar* mutable_radars(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >*
      mutable_radars();
  const ::esurfing::proto::config::Radar& radars(int index) const;
  ::esurfing::proto::config::Radar* add_radars();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >&
      radars() const;

  // repeated .esurfing.proto.config.Lidar lidars = 3;
  int lidars_size() const;
  void clear_lidars();
  static const int kLidarsFieldNumber = 3;
  ::esurfing::proto::config::Lidar* mutable_lidars(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >*
      mutable_lidars();
  const ::esurfing::proto::config::Lidar& lidars(int index) const;
  ::esurfing::proto::config::Lidar* add_lidars();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >&
      lidars() const;

  // .esurfing.proto.config.RelativePositions relative_positions = 4;
  bool has_relative_positions() const;
  void clear_relative_positions();
  static const int kRelativePositionsFieldNumber = 4;
  private:
  const ::esurfing::proto::config::RelativePositions& _internal_relative_positions() const;
  public:
  const ::esurfing::proto::config::RelativePositions& relative_positions() const;
  ::esurfing::proto::config::RelativePositions* release_relative_positions();
  ::esurfing::proto::config::RelativePositions* mutable_relative_positions();
  void set_allocated_relative_positions(::esurfing::proto::config::RelativePositions* relative_positions);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.ConfigCpc)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera > cameras_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar > radars_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar > lidars_;
  ::esurfing::proto::config::RelativePositions* relative_positions_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fcpc_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RelativePose

// bytes sensor_name = 1;
inline void RelativePose::clear_sensor_name() {
  sensor_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RelativePose::sensor_name() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RelativePose.sensor_name)
  return sensor_name_.GetNoArena();
}
inline void RelativePose::set_sensor_name(const ::std::string& value) {
  
  sensor_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RelativePose.sensor_name)
}
#if LANG_CXX11
inline void RelativePose::set_sensor_name(::std::string&& value) {
  
  sensor_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.RelativePose.sensor_name)
}
#endif
inline void RelativePose::set_sensor_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  sensor_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.RelativePose.sensor_name)
}
inline void RelativePose::set_sensor_name(const void* value, size_t size) {
  
  sensor_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.RelativePose.sensor_name)
}
inline ::std::string* RelativePose::mutable_sensor_name() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.RelativePose.sensor_name)
  return sensor_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RelativePose::release_sensor_name() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.RelativePose.sensor_name)
  
  return sensor_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RelativePose::set_allocated_sensor_name(::std::string* sensor_name) {
  if (sensor_name != NULL) {
    
  } else {
    
  }
  sensor_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sensor_name);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.RelativePose.sensor_name)
}

// .esurfing.proto.math.Transformation3d sensor_tf = 2;
inline bool RelativePose::has_sensor_tf() const {
  return this != internal_default_instance() && sensor_tf_ != NULL;
}
inline const ::esurfing::proto::math::Transformation3d& RelativePose::_internal_sensor_tf() const {
  return *sensor_tf_;
}
inline const ::esurfing::proto::math::Transformation3d& RelativePose::sensor_tf() const {
  const ::esurfing::proto::math::Transformation3d* p = sensor_tf_;
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RelativePose.sensor_tf)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Transformation3d*>(
      &::esurfing::proto::math::_Transformation3d_default_instance_);
}
inline ::esurfing::proto::math::Transformation3d* RelativePose::release_sensor_tf() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.RelativePose.sensor_tf)
  
  ::esurfing::proto::math::Transformation3d* temp = sensor_tf_;
  sensor_tf_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Transformation3d* RelativePose::mutable_sensor_tf() {
  
  if (sensor_tf_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Transformation3d>(GetArenaNoVirtual());
    sensor_tf_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.RelativePose.sensor_tf)
  return sensor_tf_;
}
inline void RelativePose::set_allocated_sensor_tf(::esurfing::proto::math::Transformation3d* sensor_tf) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(sensor_tf_);
  }
  if (sensor_tf) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      sensor_tf = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, sensor_tf, submessage_arena);
    }
    
  } else {
    
  }
  sensor_tf_ = sensor_tf;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.RelativePose.sensor_tf)
}

// -------------------------------------------------------------------

// RelativePositions

// bytes origin_sensor_name = 1;
inline void RelativePositions::clear_origin_sensor_name() {
  origin_sensor_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RelativePositions::origin_sensor_name() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RelativePositions.origin_sensor_name)
  return origin_sensor_name_.GetNoArena();
}
inline void RelativePositions::set_origin_sensor_name(const ::std::string& value) {
  
  origin_sensor_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RelativePositions.origin_sensor_name)
}
#if LANG_CXX11
inline void RelativePositions::set_origin_sensor_name(::std::string&& value) {
  
  origin_sensor_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.RelativePositions.origin_sensor_name)
}
#endif
inline void RelativePositions::set_origin_sensor_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  origin_sensor_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.RelativePositions.origin_sensor_name)
}
inline void RelativePositions::set_origin_sensor_name(const void* value, size_t size) {
  
  origin_sensor_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.RelativePositions.origin_sensor_name)
}
inline ::std::string* RelativePositions::mutable_origin_sensor_name() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.RelativePositions.origin_sensor_name)
  return origin_sensor_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RelativePositions::release_origin_sensor_name() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.RelativePositions.origin_sensor_name)
  
  return origin_sensor_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RelativePositions::set_allocated_origin_sensor_name(::std::string* origin_sensor_name) {
  if (origin_sensor_name != NULL) {
    
  } else {
    
  }
  origin_sensor_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), origin_sensor_name);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.RelativePositions.origin_sensor_name)
}

// repeated .esurfing.proto.config.RelativePose sensors = 2;
inline int RelativePositions::sensors_size() const {
  return sensors_.size();
}
inline void RelativePositions::clear_sensors() {
  sensors_.Clear();
}
inline ::esurfing::proto::config::RelativePose* RelativePositions::mutable_sensors(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.RelativePositions.sensors)
  return sensors_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RelativePose >*
RelativePositions::mutable_sensors() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.RelativePositions.sensors)
  return &sensors_;
}
inline const ::esurfing::proto::config::RelativePose& RelativePositions::sensors(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RelativePositions.sensors)
  return sensors_.Get(index);
}
inline ::esurfing::proto::config::RelativePose* RelativePositions::add_sensors() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.RelativePositions.sensors)
  return sensors_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RelativePose >&
RelativePositions::sensors() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.RelativePositions.sensors)
  return sensors_;
}

// -------------------------------------------------------------------

// ConfigCpc

// repeated .esurfing.proto.config.PinholeCamera cameras = 1;
inline int ConfigCpc::cameras_size() const {
  return cameras_.size();
}
inline ::esurfing::proto::config::PinholeCamera* ConfigCpc::mutable_cameras(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.ConfigCpc.cameras)
  return cameras_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >*
ConfigCpc::mutable_cameras() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.ConfigCpc.cameras)
  return &cameras_;
}
inline const ::esurfing::proto::config::PinholeCamera& ConfigCpc::cameras(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.ConfigCpc.cameras)
  return cameras_.Get(index);
}
inline ::esurfing::proto::config::PinholeCamera* ConfigCpc::add_cameras() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.ConfigCpc.cameras)
  return cameras_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::PinholeCamera >&
ConfigCpc::cameras() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.ConfigCpc.cameras)
  return cameras_;
}

// repeated .esurfing.proto.config.Radar radars = 2;
inline int ConfigCpc::radars_size() const {
  return radars_.size();
}
inline ::esurfing::proto::config::Radar* ConfigCpc::mutable_radars(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.ConfigCpc.radars)
  return radars_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >*
ConfigCpc::mutable_radars() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.ConfigCpc.radars)
  return &radars_;
}
inline const ::esurfing::proto::config::Radar& ConfigCpc::radars(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.ConfigCpc.radars)
  return radars_.Get(index);
}
inline ::esurfing::proto::config::Radar* ConfigCpc::add_radars() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.ConfigCpc.radars)
  return radars_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >&
ConfigCpc::radars() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.ConfigCpc.radars)
  return radars_;
}

// repeated .esurfing.proto.config.Lidar lidars = 3;
inline int ConfigCpc::lidars_size() const {
  return lidars_.size();
}
inline ::esurfing::proto::config::Lidar* ConfigCpc::mutable_lidars(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.ConfigCpc.lidars)
  return lidars_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >*
ConfigCpc::mutable_lidars() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.ConfigCpc.lidars)
  return &lidars_;
}
inline const ::esurfing::proto::config::Lidar& ConfigCpc::lidars(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.ConfigCpc.lidars)
  return lidars_.Get(index);
}
inline ::esurfing::proto::config::Lidar* ConfigCpc::add_lidars() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.ConfigCpc.lidars)
  return lidars_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Lidar >&
ConfigCpc::lidars() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.ConfigCpc.lidars)
  return lidars_;
}

// .esurfing.proto.config.RelativePositions relative_positions = 4;
inline bool ConfigCpc::has_relative_positions() const {
  return this != internal_default_instance() && relative_positions_ != NULL;
}
inline void ConfigCpc::clear_relative_positions() {
  if (GetArenaNoVirtual() == NULL && relative_positions_ != NULL) {
    delete relative_positions_;
  }
  relative_positions_ = NULL;
}
inline const ::esurfing::proto::config::RelativePositions& ConfigCpc::_internal_relative_positions() const {
  return *relative_positions_;
}
inline const ::esurfing::proto::config::RelativePositions& ConfigCpc::relative_positions() const {
  const ::esurfing::proto::config::RelativePositions* p = relative_positions_;
  // @@protoc_insertion_point(field_get:esurfing.proto.config.ConfigCpc.relative_positions)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::config::RelativePositions*>(
      &::esurfing::proto::config::_RelativePositions_default_instance_);
}
inline ::esurfing::proto::config::RelativePositions* ConfigCpc::release_relative_positions() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.ConfigCpc.relative_positions)
  
  ::esurfing::proto::config::RelativePositions* temp = relative_positions_;
  relative_positions_ = NULL;
  return temp;
}
inline ::esurfing::proto::config::RelativePositions* ConfigCpc::mutable_relative_positions() {
  
  if (relative_positions_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::config::RelativePositions>(GetArenaNoVirtual());
    relative_positions_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.ConfigCpc.relative_positions)
  return relative_positions_;
}
inline void ConfigCpc::set_allocated_relative_positions(::esurfing::proto::config::RelativePositions* relative_positions) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete relative_positions_;
  }
  if (relative_positions) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      relative_positions = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, relative_positions, submessage_arena);
    }
    
  } else {
    
  }
  relative_positions_ = relative_positions;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.ConfigCpc.relative_positions)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace config
}  // namespace proto
}  // namespace esurfing

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fcpc_2eproto
