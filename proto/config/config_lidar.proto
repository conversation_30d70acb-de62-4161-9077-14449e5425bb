// Copyright
// Author:

syntax = "proto3";

package esurfing.proto.config;

import "proto/math/geo.proto";

option go_package = "proto/config/config_lidar";
option java_package = "proto.config";
option java_outer_classname = "LidarConfig";
option java_multiple_files = false;

message LidarIntrisic {
  bool enabled = 1;
  int32 min_intensity = 2;
  int32 max_intensity = 3;

  float rot = 4;
  float vert = 5;
  float dist = 6;
  float dist_x = 7;
  float dist_y = 8;
  float vert_offset = 9;
  float horiz_offset = 10;
  float focal_distance = 11;
  float focal_scope = 12;
  int32 ring = 13;
  float cos_rot = 14;
  float sin_rot = 15;
  float cos_vert = 16;
  float sin_vert = 17;
}

message Lidar {
  bytes name = 1;
  enum Type {
    UNKNOWN = 0;
    VLP_16 = 1;
    VLP_32 = 2;
    HESAI_64 = 3;
    RS_80 = 4;
    RS_128 = 5;
  }
  Type type = 2;
  repeated LidarIntrisic intrinsics = 3;

  float min_distance = 4;
  float max_distance = 5;

  math.Transformation3d tf_vehicle_lidar = 6;

  bytes topic = 7;
  int32 usage = 8;
}

message ConfigLidars {
  repeated Lidar lidars = 1;
}
