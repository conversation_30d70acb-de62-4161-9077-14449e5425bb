// Copyright
// Author:

syntax = "proto3";

package esurfing.proto.config;

import "proto/math/geo.proto";

option go_package = "proto/config/config_camera";
option java_package = "proto.config";
option java_outer_classname = "CameraConfig";
option java_multiple_files = false;

message PinholeCamera {
  bytes name = 1;
  int32 image_width = 2;
  int32 image_height = 3;
  double frame_rate = 4;

  math.Transformation3d tf_vehicle_camera = 5;
  double fx = 6;
  double fy = 7;
  double cx = 8;
  double cy = 9;
  repeated double radial_distortion = 10;
  bool flip = 11;
  bool crop = 12;

  bytes topic = 13;
  bool time_synced = 14;

  enum Usage {
    UNKNOWN = 0;
    VISION = 1;
    SIGNAL = 2;
    ALL = 3;
  }

  int32 usage = 15;
}

message ConfigCameras {
  repeated PinholeCamera cameras = 1;
}
