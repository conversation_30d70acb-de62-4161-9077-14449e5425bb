// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/config/config_lidar.proto

#include "proto/config/config_lidar.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5flidar_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_LidarIntrisic;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5flidar_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Lidar;
}  // namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto
namespace protobuf_proto_2fmath_2fgeo_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Transformation3d;
}  // namespace protobuf_proto_2fmath_2fgeo_2eproto
namespace esurfing {
namespace proto {
namespace config {
class LidarIntrisicDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LidarIntrisic>
      _instance;
} _LidarIntrisic_default_instance_;
class LidarDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Lidar>
      _instance;
} _Lidar_default_instance_;
class ConfigLidarsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConfigLidars>
      _instance;
} _ConfigLidars_default_instance_;
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto {
static void InitDefaultsLidarIntrisic() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_LidarIntrisic_default_instance_;
    new (ptr) ::esurfing::proto::config::LidarIntrisic();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::LidarIntrisic::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_LidarIntrisic =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsLidarIntrisic}, {}};

static void InitDefaultsLidar() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_Lidar_default_instance_;
    new (ptr) ::esurfing::proto::config::Lidar();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::Lidar::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_Lidar =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsLidar}, {
      &protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_LidarIntrisic.base,
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Transformation3d.base,}};

static void InitDefaultsConfigLidars() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::config::_ConfigLidars_default_instance_;
    new (ptr) ::esurfing::proto::config::ConfigLidars();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::config::ConfigLidars::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ConfigLidars =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsConfigLidars}, {
      &protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_Lidar.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_LidarIntrisic.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Lidar.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ConfigLidars.base);
}

::google::protobuf::Metadata file_level_metadata[3];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, enabled_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, min_intensity_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, max_intensity_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, rot_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, vert_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, dist_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, dist_x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, dist_y_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, vert_offset_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, horiz_offset_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, focal_distance_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, focal_scope_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, ring_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, cos_rot_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, sin_rot_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, cos_vert_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::LidarIntrisic, sin_vert_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, intrinsics_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, min_distance_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, max_distance_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, tf_vehicle_lidar_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, topic_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::Lidar, usage_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigLidars, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::config::ConfigLidars, lidars_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::esurfing::proto::config::LidarIntrisic)},
  { 22, -1, sizeof(::esurfing::proto::config::Lidar)},
  { 35, -1, sizeof(::esurfing::proto::config::ConfigLidars)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_LidarIntrisic_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_Lidar_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::config::_ConfigLidars_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "proto/config/config_lidar.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\037proto/config/config_lidar.proto\022\025esurf"
      "ing.proto.config\032\024proto/math/geo.proto\"\303"
      "\002\n\rLidarIntrisic\022\017\n\007enabled\030\001 \001(\010\022\025\n\rmin"
      "_intensity\030\002 \001(\005\022\025\n\rmax_intensity\030\003 \001(\005\022"
      "\013\n\003rot\030\004 \001(\002\022\014\n\004vert\030\005 \001(\002\022\014\n\004dist\030\006 \001(\002"
      "\022\016\n\006dist_x\030\007 \001(\002\022\016\n\006dist_y\030\010 \001(\002\022\023\n\013vert"
      "_offset\030\t \001(\002\022\024\n\014horiz_offset\030\n \001(\002\022\026\n\016f"
      "ocal_distance\030\013 \001(\002\022\023\n\013focal_scope\030\014 \001(\002"
      "\022\014\n\004ring\030\r \001(\005\022\017\n\007cos_rot\030\016 \001(\002\022\017\n\007sin_r"
      "ot\030\017 \001(\002\022\020\n\010cos_vert\030\020 \001(\002\022\020\n\010sin_vert\030\021"
      " \001(\002\"\335\002\n\005Lidar\022\014\n\004name\030\001 \001(\014\022/\n\004type\030\002 \001"
      "(\0162!.esurfing.proto.config.Lidar.Type\0228\n"
      "\nintrinsics\030\003 \003(\0132$.esurfing.proto.confi"
      "g.LidarIntrisic\022\024\n\014min_distance\030\004 \001(\002\022\024\n"
      "\014max_distance\030\005 \001(\002\022\?\n\020tf_vehicle_lidar\030"
      "\006 \001(\0132%.esurfing.proto.math.Transformati"
      "on3d\022\r\n\005topic\030\007 \001(\014\022\r\n\005usage\030\010 \001(\005\"P\n\004Ty"
      "pe\022\013\n\007UNKNOWN\020\000\022\n\n\006VLP_16\020\001\022\n\n\006VLP_32\020\002\022"
      "\014\n\010HESAI_64\020\003\022\t\n\005RS_80\020\004\022\n\n\006RS_128\020\005\"<\n\014"
      "ConfigLidars\022,\n\006lidars\030\001 \003(\0132\034.esurfing."
      "proto.config.LidarB8\n\014proto.configB\013Lida"
      "rConfigP\000Z\031proto/config/config_lidarb\006pr"
      "oto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 884);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "proto/config/config_lidar.proto", &protobuf_RegisterTypes);
  ::protobuf_proto_2fmath_2fgeo_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_proto_2fconfig_2fconfig_5flidar_2eproto
namespace esurfing {
namespace proto {
namespace config {
const ::google::protobuf::EnumDescriptor* Lidar_Type_descriptor() {
  protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::file_level_enum_descriptors[0];
}
bool Lidar_Type_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const Lidar_Type Lidar::UNKNOWN;
const Lidar_Type Lidar::VLP_16;
const Lidar_Type Lidar::VLP_32;
const Lidar_Type Lidar::HESAI_64;
const Lidar_Type Lidar::RS_80;
const Lidar_Type Lidar::RS_128;
const Lidar_Type Lidar::Type_MIN;
const Lidar_Type Lidar::Type_MAX;
const int Lidar::Type_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void LidarIntrisic::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LidarIntrisic::kEnabledFieldNumber;
const int LidarIntrisic::kMinIntensityFieldNumber;
const int LidarIntrisic::kMaxIntensityFieldNumber;
const int LidarIntrisic::kRotFieldNumber;
const int LidarIntrisic::kVertFieldNumber;
const int LidarIntrisic::kDistFieldNumber;
const int LidarIntrisic::kDistXFieldNumber;
const int LidarIntrisic::kDistYFieldNumber;
const int LidarIntrisic::kVertOffsetFieldNumber;
const int LidarIntrisic::kHorizOffsetFieldNumber;
const int LidarIntrisic::kFocalDistanceFieldNumber;
const int LidarIntrisic::kFocalScopeFieldNumber;
const int LidarIntrisic::kRingFieldNumber;
const int LidarIntrisic::kCosRotFieldNumber;
const int LidarIntrisic::kSinRotFieldNumber;
const int LidarIntrisic::kCosVertFieldNumber;
const int LidarIntrisic::kSinVertFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LidarIntrisic::LidarIntrisic()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_LidarIntrisic.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.LidarIntrisic)
}
LidarIntrisic::LidarIntrisic(const LidarIntrisic& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&enabled_, &from.enabled_,
    static_cast<size_t>(reinterpret_cast<char*>(&sin_vert_) -
    reinterpret_cast<char*>(&enabled_)) + sizeof(sin_vert_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.LidarIntrisic)
}

void LidarIntrisic::SharedCtor() {
  ::memset(&enabled_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sin_vert_) -
      reinterpret_cast<char*>(&enabled_)) + sizeof(sin_vert_));
}

LidarIntrisic::~LidarIntrisic() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.LidarIntrisic)
  SharedDtor();
}

void LidarIntrisic::SharedDtor() {
}

void LidarIntrisic::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* LidarIntrisic::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LidarIntrisic& LidarIntrisic::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_LidarIntrisic.base);
  return *internal_default_instance();
}


void LidarIntrisic::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.LidarIntrisic)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&enabled_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sin_vert_) -
      reinterpret_cast<char*>(&enabled_)) + sizeof(sin_vert_));
  _internal_metadata_.Clear();
}

bool LidarIntrisic::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.LidarIntrisic)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool enabled = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &enabled_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 min_intensity = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &min_intensity_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 max_intensity = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &max_intensity_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float rot = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &rot_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float vert = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &vert_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float dist = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(53u /* 53 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &dist_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float dist_x = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(61u /* 61 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &dist_x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float dist_y = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(69u /* 69 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &dist_y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float vert_offset = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(77u /* 77 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &vert_offset_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float horiz_offset = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(85u /* 85 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &horiz_offset_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float focal_distance = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(93u /* 93 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &focal_distance_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float focal_scope = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(101u /* 101 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &focal_scope_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 ring = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ring_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float cos_rot = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(117u /* 117 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &cos_rot_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float sin_rot = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(125u /* 125 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &sin_rot_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float cos_vert = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(133u /* 133 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &cos_vert_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float sin_vert = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(141u /* 141 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &sin_vert_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.LidarIntrisic)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.LidarIntrisic)
  return false;
#undef DO_
}

void LidarIntrisic::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.LidarIntrisic)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->enabled() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->enabled(), output);
  }

  // int32 min_intensity = 2;
  if (this->min_intensity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->min_intensity(), output);
  }

  // int32 max_intensity = 3;
  if (this->max_intensity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->max_intensity(), output);
  }

  // float rot = 4;
  if (this->rot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->rot(), output);
  }

  // float vert = 5;
  if (this->vert() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->vert(), output);
  }

  // float dist = 6;
  if (this->dist() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(6, this->dist(), output);
  }

  // float dist_x = 7;
  if (this->dist_x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(7, this->dist_x(), output);
  }

  // float dist_y = 8;
  if (this->dist_y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(8, this->dist_y(), output);
  }

  // float vert_offset = 9;
  if (this->vert_offset() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(9, this->vert_offset(), output);
  }

  // float horiz_offset = 10;
  if (this->horiz_offset() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(10, this->horiz_offset(), output);
  }

  // float focal_distance = 11;
  if (this->focal_distance() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(11, this->focal_distance(), output);
  }

  // float focal_scope = 12;
  if (this->focal_scope() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(12, this->focal_scope(), output);
  }

  // int32 ring = 13;
  if (this->ring() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->ring(), output);
  }

  // float cos_rot = 14;
  if (this->cos_rot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(14, this->cos_rot(), output);
  }

  // float sin_rot = 15;
  if (this->sin_rot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(15, this->sin_rot(), output);
  }

  // float cos_vert = 16;
  if (this->cos_vert() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(16, this->cos_vert(), output);
  }

  // float sin_vert = 17;
  if (this->sin_vert() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(17, this->sin_vert(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.LidarIntrisic)
}

::google::protobuf::uint8* LidarIntrisic::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.LidarIntrisic)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->enabled() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->enabled(), target);
  }

  // int32 min_intensity = 2;
  if (this->min_intensity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->min_intensity(), target);
  }

  // int32 max_intensity = 3;
  if (this->max_intensity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->max_intensity(), target);
  }

  // float rot = 4;
  if (this->rot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->rot(), target);
  }

  // float vert = 5;
  if (this->vert() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->vert(), target);
  }

  // float dist = 6;
  if (this->dist() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(6, this->dist(), target);
  }

  // float dist_x = 7;
  if (this->dist_x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(7, this->dist_x(), target);
  }

  // float dist_y = 8;
  if (this->dist_y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(8, this->dist_y(), target);
  }

  // float vert_offset = 9;
  if (this->vert_offset() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(9, this->vert_offset(), target);
  }

  // float horiz_offset = 10;
  if (this->horiz_offset() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(10, this->horiz_offset(), target);
  }

  // float focal_distance = 11;
  if (this->focal_distance() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(11, this->focal_distance(), target);
  }

  // float focal_scope = 12;
  if (this->focal_scope() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(12, this->focal_scope(), target);
  }

  // int32 ring = 13;
  if (this->ring() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->ring(), target);
  }

  // float cos_rot = 14;
  if (this->cos_rot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(14, this->cos_rot(), target);
  }

  // float sin_rot = 15;
  if (this->sin_rot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(15, this->sin_rot(), target);
  }

  // float cos_vert = 16;
  if (this->cos_vert() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(16, this->cos_vert(), target);
  }

  // float sin_vert = 17;
  if (this->sin_vert() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(17, this->sin_vert(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.LidarIntrisic)
  return target;
}

size_t LidarIntrisic::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.LidarIntrisic)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bool enabled = 1;
  if (this->enabled() != 0) {
    total_size += 1 + 1;
  }

  // int32 min_intensity = 2;
  if (this->min_intensity() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->min_intensity());
  }

  // int32 max_intensity = 3;
  if (this->max_intensity() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->max_intensity());
  }

  // float rot = 4;
  if (this->rot() != 0) {
    total_size += 1 + 4;
  }

  // float vert = 5;
  if (this->vert() != 0) {
    total_size += 1 + 4;
  }

  // float dist = 6;
  if (this->dist() != 0) {
    total_size += 1 + 4;
  }

  // float dist_x = 7;
  if (this->dist_x() != 0) {
    total_size += 1 + 4;
  }

  // float dist_y = 8;
  if (this->dist_y() != 0) {
    total_size += 1 + 4;
  }

  // float vert_offset = 9;
  if (this->vert_offset() != 0) {
    total_size += 1 + 4;
  }

  // float horiz_offset = 10;
  if (this->horiz_offset() != 0) {
    total_size += 1 + 4;
  }

  // float focal_distance = 11;
  if (this->focal_distance() != 0) {
    total_size += 1 + 4;
  }

  // float focal_scope = 12;
  if (this->focal_scope() != 0) {
    total_size += 1 + 4;
  }

  // int32 ring = 13;
  if (this->ring() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ring());
  }

  // float cos_rot = 14;
  if (this->cos_rot() != 0) {
    total_size += 1 + 4;
  }

  // float sin_rot = 15;
  if (this->sin_rot() != 0) {
    total_size += 1 + 4;
  }

  // float cos_vert = 16;
  if (this->cos_vert() != 0) {
    total_size += 2 + 4;
  }

  // float sin_vert = 17;
  if (this->sin_vert() != 0) {
    total_size += 2 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LidarIntrisic::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.LidarIntrisic)
  GOOGLE_DCHECK_NE(&from, this);
  const LidarIntrisic* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LidarIntrisic>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.LidarIntrisic)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.LidarIntrisic)
    MergeFrom(*source);
  }
}

void LidarIntrisic::MergeFrom(const LidarIntrisic& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.LidarIntrisic)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.enabled() != 0) {
    set_enabled(from.enabled());
  }
  if (from.min_intensity() != 0) {
    set_min_intensity(from.min_intensity());
  }
  if (from.max_intensity() != 0) {
    set_max_intensity(from.max_intensity());
  }
  if (from.rot() != 0) {
    set_rot(from.rot());
  }
  if (from.vert() != 0) {
    set_vert(from.vert());
  }
  if (from.dist() != 0) {
    set_dist(from.dist());
  }
  if (from.dist_x() != 0) {
    set_dist_x(from.dist_x());
  }
  if (from.dist_y() != 0) {
    set_dist_y(from.dist_y());
  }
  if (from.vert_offset() != 0) {
    set_vert_offset(from.vert_offset());
  }
  if (from.horiz_offset() != 0) {
    set_horiz_offset(from.horiz_offset());
  }
  if (from.focal_distance() != 0) {
    set_focal_distance(from.focal_distance());
  }
  if (from.focal_scope() != 0) {
    set_focal_scope(from.focal_scope());
  }
  if (from.ring() != 0) {
    set_ring(from.ring());
  }
  if (from.cos_rot() != 0) {
    set_cos_rot(from.cos_rot());
  }
  if (from.sin_rot() != 0) {
    set_sin_rot(from.sin_rot());
  }
  if (from.cos_vert() != 0) {
    set_cos_vert(from.cos_vert());
  }
  if (from.sin_vert() != 0) {
    set_sin_vert(from.sin_vert());
  }
}

void LidarIntrisic::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.LidarIntrisic)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LidarIntrisic::CopyFrom(const LidarIntrisic& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.LidarIntrisic)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LidarIntrisic::IsInitialized() const {
  return true;
}

void LidarIntrisic::Swap(LidarIntrisic* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LidarIntrisic::InternalSwap(LidarIntrisic* other) {
  using std::swap;
  swap(enabled_, other->enabled_);
  swap(min_intensity_, other->min_intensity_);
  swap(max_intensity_, other->max_intensity_);
  swap(rot_, other->rot_);
  swap(vert_, other->vert_);
  swap(dist_, other->dist_);
  swap(dist_x_, other->dist_x_);
  swap(dist_y_, other->dist_y_);
  swap(vert_offset_, other->vert_offset_);
  swap(horiz_offset_, other->horiz_offset_);
  swap(focal_distance_, other->focal_distance_);
  swap(focal_scope_, other->focal_scope_);
  swap(ring_, other->ring_);
  swap(cos_rot_, other->cos_rot_);
  swap(sin_rot_, other->sin_rot_);
  swap(cos_vert_, other->cos_vert_);
  swap(sin_vert_, other->sin_vert_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata LidarIntrisic::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Lidar::InitAsDefaultInstance() {
  ::esurfing::proto::config::_Lidar_default_instance_._instance.get_mutable()->tf_vehicle_lidar_ = const_cast< ::esurfing::proto::math::Transformation3d*>(
      ::esurfing::proto::math::Transformation3d::internal_default_instance());
}
void Lidar::clear_tf_vehicle_lidar() {
  if (GetArenaNoVirtual() == NULL && tf_vehicle_lidar_ != NULL) {
    delete tf_vehicle_lidar_;
  }
  tf_vehicle_lidar_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Lidar::kNameFieldNumber;
const int Lidar::kTypeFieldNumber;
const int Lidar::kIntrinsicsFieldNumber;
const int Lidar::kMinDistanceFieldNumber;
const int Lidar::kMaxDistanceFieldNumber;
const int Lidar::kTfVehicleLidarFieldNumber;
const int Lidar::kTopicFieldNumber;
const int Lidar::kUsageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Lidar::Lidar()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_Lidar.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.Lidar)
}
Lidar::Lidar(const Lidar& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      intrinsics_(from.intrinsics_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  topic_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.topic().size() > 0) {
    topic_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.topic_);
  }
  if (from.has_tf_vehicle_lidar()) {
    tf_vehicle_lidar_ = new ::esurfing::proto::math::Transformation3d(*from.tf_vehicle_lidar_);
  } else {
    tf_vehicle_lidar_ = NULL;
  }
  ::memcpy(&type_, &from.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&usage_) -
    reinterpret_cast<char*>(&type_)) + sizeof(usage_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.Lidar)
}

void Lidar::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  topic_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tf_vehicle_lidar_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&usage_) -
      reinterpret_cast<char*>(&tf_vehicle_lidar_)) + sizeof(usage_));
}

Lidar::~Lidar() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.Lidar)
  SharedDtor();
}

void Lidar::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  topic_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tf_vehicle_lidar_;
}

void Lidar::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Lidar::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Lidar& Lidar::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_Lidar.base);
  return *internal_default_instance();
}


void Lidar::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.Lidar)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  intrinsics_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  topic_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && tf_vehicle_lidar_ != NULL) {
    delete tf_vehicle_lidar_;
  }
  tf_vehicle_lidar_ = NULL;
  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&usage_) -
      reinterpret_cast<char*>(&type_)) + sizeof(usage_));
  _internal_metadata_.Clear();
}

bool Lidar::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.Lidar)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bytes name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_name()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .esurfing.proto.config.Lidar.Type type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::esurfing::proto::config::Lidar_Type >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .esurfing.proto.config.LidarIntrisic intrinsics = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_intrinsics()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float min_distance = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &min_distance_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float max_distance = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &max_distance_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .esurfing.proto.math.Transformation3d tf_vehicle_lidar = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tf_vehicle_lidar()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes topic = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_topic()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 usage = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &usage_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.Lidar)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.Lidar)
  return false;
#undef DO_
}

void Lidar::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.Lidar)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->name(), output);
  }

  // .esurfing.proto.config.Lidar.Type type = 2;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->type(), output);
  }

  // repeated .esurfing.proto.config.LidarIntrisic intrinsics = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->intrinsics_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->intrinsics(static_cast<int>(i)),
      output);
  }

  // float min_distance = 4;
  if (this->min_distance() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->min_distance(), output);
  }

  // float max_distance = 5;
  if (this->max_distance() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->max_distance(), output);
  }

  // .esurfing.proto.math.Transformation3d tf_vehicle_lidar = 6;
  if (this->has_tf_vehicle_lidar()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_tf_vehicle_lidar(), output);
  }

  // bytes topic = 7;
  if (this->topic().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      7, this->topic(), output);
  }

  // int32 usage = 8;
  if (this->usage() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->usage(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.Lidar)
}

::google::protobuf::uint8* Lidar::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.Lidar)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes name = 1;
  if (this->name().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->name(), target);
  }

  // .esurfing.proto.config.Lidar.Type type = 2;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->type(), target);
  }

  // repeated .esurfing.proto.config.LidarIntrisic intrinsics = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->intrinsics_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->intrinsics(static_cast<int>(i)), deterministic, target);
  }

  // float min_distance = 4;
  if (this->min_distance() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->min_distance(), target);
  }

  // float max_distance = 5;
  if (this->max_distance() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->max_distance(), target);
  }

  // .esurfing.proto.math.Transformation3d tf_vehicle_lidar = 6;
  if (this->has_tf_vehicle_lidar()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_tf_vehicle_lidar(), deterministic, target);
  }

  // bytes topic = 7;
  if (this->topic().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        7, this->topic(), target);
  }

  // int32 usage = 8;
  if (this->usage() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->usage(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.Lidar)
  return target;
}

size_t Lidar::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.Lidar)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.config.LidarIntrisic intrinsics = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->intrinsics_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->intrinsics(static_cast<int>(i)));
    }
  }

  // bytes name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->name());
  }

  // bytes topic = 7;
  if (this->topic().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->topic());
  }

  // .esurfing.proto.math.Transformation3d tf_vehicle_lidar = 6;
  if (this->has_tf_vehicle_lidar()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tf_vehicle_lidar_);
  }

  // .esurfing.proto.config.Lidar.Type type = 2;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  // float min_distance = 4;
  if (this->min_distance() != 0) {
    total_size += 1 + 4;
  }

  // float max_distance = 5;
  if (this->max_distance() != 0) {
    total_size += 1 + 4;
  }

  // int32 usage = 8;
  if (this->usage() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->usage());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Lidar::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.Lidar)
  GOOGLE_DCHECK_NE(&from, this);
  const Lidar* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Lidar>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.Lidar)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.Lidar)
    MergeFrom(*source);
  }
}

void Lidar::MergeFrom(const Lidar& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.Lidar)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  intrinsics_.MergeFrom(from.intrinsics_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.topic().size() > 0) {

    topic_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.topic_);
  }
  if (from.has_tf_vehicle_lidar()) {
    mutable_tf_vehicle_lidar()->::esurfing::proto::math::Transformation3d::MergeFrom(from.tf_vehicle_lidar());
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
  if (from.min_distance() != 0) {
    set_min_distance(from.min_distance());
  }
  if (from.max_distance() != 0) {
    set_max_distance(from.max_distance());
  }
  if (from.usage() != 0) {
    set_usage(from.usage());
  }
}

void Lidar::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.Lidar)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Lidar::CopyFrom(const Lidar& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.Lidar)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Lidar::IsInitialized() const {
  return true;
}

void Lidar::Swap(Lidar* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Lidar::InternalSwap(Lidar* other) {
  using std::swap;
  CastToBase(&intrinsics_)->InternalSwap(CastToBase(&other->intrinsics_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  topic_.Swap(&other->topic_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tf_vehicle_lidar_, other->tf_vehicle_lidar_);
  swap(type_, other->type_);
  swap(min_distance_, other->min_distance_);
  swap(max_distance_, other->max_distance_);
  swap(usage_, other->usage_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Lidar::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ConfigLidars::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ConfigLidars::kLidarsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ConfigLidars::ConfigLidars()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_ConfigLidars.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.config.ConfigLidars)
}
ConfigLidars::ConfigLidars(const ConfigLidars& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      lidars_(from.lidars_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.config.ConfigLidars)
}

void ConfigLidars::SharedCtor() {
}

ConfigLidars::~ConfigLidars() {
  // @@protoc_insertion_point(destructor:esurfing.proto.config.ConfigLidars)
  SharedDtor();
}

void ConfigLidars::SharedDtor() {
}

void ConfigLidars::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ConfigLidars::descriptor() {
  ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ConfigLidars& ConfigLidars::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::scc_info_ConfigLidars.base);
  return *internal_default_instance();
}


void ConfigLidars::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.config.ConfigLidars)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lidars_.Clear();
  _internal_metadata_.Clear();
}

bool ConfigLidars::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.config.ConfigLidars)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .esurfing.proto.config.Lidar lidars = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_lidars()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.config.ConfigLidars)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.config.ConfigLidars)
  return false;
#undef DO_
}

void ConfigLidars::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.config.ConfigLidars)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.config.Lidar lidars = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->lidars_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->lidars(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.config.ConfigLidars)
}

::google::protobuf::uint8* ConfigLidars::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.config.ConfigLidars)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.config.Lidar lidars = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->lidars_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->lidars(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.config.ConfigLidars)
  return target;
}

size_t ConfigLidars::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.config.ConfigLidars)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.config.Lidar lidars = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->lidars_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->lidars(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConfigLidars::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.config.ConfigLidars)
  GOOGLE_DCHECK_NE(&from, this);
  const ConfigLidars* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ConfigLidars>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.config.ConfigLidars)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.config.ConfigLidars)
    MergeFrom(*source);
  }
}

void ConfigLidars::MergeFrom(const ConfigLidars& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.config.ConfigLidars)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  lidars_.MergeFrom(from.lidars_);
}

void ConfigLidars::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.config.ConfigLidars)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConfigLidars::CopyFrom(const ConfigLidars& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.config.ConfigLidars)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigLidars::IsInitialized() const {
  return true;
}

void ConfigLidars::Swap(ConfigLidars* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ConfigLidars::InternalSwap(ConfigLidars* other) {
  using std::swap;
  CastToBase(&lidars_)->InternalSwap(CastToBase(&other->lidars_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ConfigLidars::GetMetadata() const {
  protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fconfig_2fconfig_5flidar_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::LidarIntrisic* Arena::CreateMaybeMessage< ::esurfing::proto::config::LidarIntrisic >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::LidarIntrisic >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::Lidar* Arena::CreateMaybeMessage< ::esurfing::proto::config::Lidar >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::Lidar >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::config::ConfigLidars* Arena::CreateMaybeMessage< ::esurfing::proto::config::ConfigLidars >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::config::ConfigLidars >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)
