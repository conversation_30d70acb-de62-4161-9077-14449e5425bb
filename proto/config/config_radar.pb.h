// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/config/config_radar.proto

#ifndef PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fradar_2eproto
#define PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fradar_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/math/geo.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fconfig_2fconfig_5fradar_2eproto 

namespace protobuf_proto_2fconfig_2fconfig_5fradar_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[3];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_proto_2fconfig_2fconfig_5fradar_2eproto
namespace esurfing {
namespace proto {
namespace config {
class ConfigRadars;
class ConfigRadarsDefaultTypeInternal;
extern ConfigRadarsDefaultTypeInternal _ConfigRadars_default_instance_;
class Radar;
class RadarDefaultTypeInternal;
extern RadarDefaultTypeInternal _Radar_default_instance_;
class RadarIntrisic;
class RadarIntrisicDefaultTypeInternal;
extern RadarIntrisicDefaultTypeInternal _RadarIntrisic_default_instance_;
}  // namespace config
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> ::esurfing::proto::config::ConfigRadars* Arena::CreateMaybeMessage<::esurfing::proto::config::ConfigRadars>(Arena*);
template<> ::esurfing::proto::config::Radar* Arena::CreateMaybeMessage<::esurfing::proto::config::Radar>(Arena*);
template<> ::esurfing::proto::config::RadarIntrisic* Arena::CreateMaybeMessage<::esurfing::proto::config::RadarIntrisic>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace esurfing {
namespace proto {
namespace config {

// ===================================================================

class RadarIntrisic : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.RadarIntrisic) */ {
 public:
  RadarIntrisic();
  virtual ~RadarIntrisic();

  RadarIntrisic(const RadarIntrisic& from);

  inline RadarIntrisic& operator=(const RadarIntrisic& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RadarIntrisic(RadarIntrisic&& from) noexcept
    : RadarIntrisic() {
    *this = ::std::move(from);
  }

  inline RadarIntrisic& operator=(RadarIntrisic&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RadarIntrisic& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RadarIntrisic* internal_default_instance() {
    return reinterpret_cast<const RadarIntrisic*>(
               &_RadarIntrisic_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(RadarIntrisic* other);
  friend void swap(RadarIntrisic& a, RadarIntrisic& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RadarIntrisic* New() const final {
    return CreateMaybeMessage<RadarIntrisic>(NULL);
  }

  RadarIntrisic* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RadarIntrisic>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RadarIntrisic& from);
  void MergeFrom(const RadarIntrisic& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RadarIntrisic* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bool enabled = 1;
  void clear_enabled();
  static const int kEnabledFieldNumber = 1;
  bool enabled() const;
  void set_enabled(bool value);

  // int32 min_intensity = 2;
  void clear_min_intensity();
  static const int kMinIntensityFieldNumber = 2;
  ::google::protobuf::int32 min_intensity() const;
  void set_min_intensity(::google::protobuf::int32 value);

  // int32 max_intensity = 3;
  void clear_max_intensity();
  static const int kMaxIntensityFieldNumber = 3;
  ::google::protobuf::int32 max_intensity() const;
  void set_max_intensity(::google::protobuf::int32 value);

  // float rot = 4;
  void clear_rot();
  static const int kRotFieldNumber = 4;
  float rot() const;
  void set_rot(float value);

  // float vert = 5;
  void clear_vert();
  static const int kVertFieldNumber = 5;
  float vert() const;
  void set_vert(float value);

  // float dist = 6;
  void clear_dist();
  static const int kDistFieldNumber = 6;
  float dist() const;
  void set_dist(float value);

  // float dist_x = 7;
  void clear_dist_x();
  static const int kDistXFieldNumber = 7;
  float dist_x() const;
  void set_dist_x(float value);

  // float dist_y = 8;
  void clear_dist_y();
  static const int kDistYFieldNumber = 8;
  float dist_y() const;
  void set_dist_y(float value);

  // float vert_offset = 9;
  void clear_vert_offset();
  static const int kVertOffsetFieldNumber = 9;
  float vert_offset() const;
  void set_vert_offset(float value);

  // float horiz_offset = 10;
  void clear_horiz_offset();
  static const int kHorizOffsetFieldNumber = 10;
  float horiz_offset() const;
  void set_horiz_offset(float value);

  // float focal_distance = 11;
  void clear_focal_distance();
  static const int kFocalDistanceFieldNumber = 11;
  float focal_distance() const;
  void set_focal_distance(float value);

  // float focal_scope = 12;
  void clear_focal_scope();
  static const int kFocalScopeFieldNumber = 12;
  float focal_scope() const;
  void set_focal_scope(float value);

  // int32 ring = 13;
  void clear_ring();
  static const int kRingFieldNumber = 13;
  ::google::protobuf::int32 ring() const;
  void set_ring(::google::protobuf::int32 value);

  // float cos_rot = 14;
  void clear_cos_rot();
  static const int kCosRotFieldNumber = 14;
  float cos_rot() const;
  void set_cos_rot(float value);

  // float sin_rot = 15;
  void clear_sin_rot();
  static const int kSinRotFieldNumber = 15;
  float sin_rot() const;
  void set_sin_rot(float value);

  // float cos_vert = 16;
  void clear_cos_vert();
  static const int kCosVertFieldNumber = 16;
  float cos_vert() const;
  void set_cos_vert(float value);

  // float sin_vert = 17;
  void clear_sin_vert();
  static const int kSinVertFieldNumber = 17;
  float sin_vert() const;
  void set_sin_vert(float value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.RadarIntrisic)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  bool enabled_;
  ::google::protobuf::int32 min_intensity_;
  ::google::protobuf::int32 max_intensity_;
  float rot_;
  float vert_;
  float dist_;
  float dist_x_;
  float dist_y_;
  float vert_offset_;
  float horiz_offset_;
  float focal_distance_;
  float focal_scope_;
  ::google::protobuf::int32 ring_;
  float cos_rot_;
  float sin_rot_;
  float cos_vert_;
  float sin_vert_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fradar_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Radar : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.Radar) */ {
 public:
  Radar();
  virtual ~Radar();

  Radar(const Radar& from);

  inline Radar& operator=(const Radar& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Radar(Radar&& from) noexcept
    : Radar() {
    *this = ::std::move(from);
  }

  inline Radar& operator=(Radar&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Radar& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Radar* internal_default_instance() {
    return reinterpret_cast<const Radar*>(
               &_Radar_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(Radar* other);
  friend void swap(Radar& a, Radar& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Radar* New() const final {
    return CreateMaybeMessage<Radar>(NULL);
  }

  Radar* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Radar>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Radar& from);
  void MergeFrom(const Radar& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Radar* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.config.RadarIntrisic intrinsics = 3;
  int intrinsics_size() const;
  void clear_intrinsics();
  static const int kIntrinsicsFieldNumber = 3;
  ::esurfing::proto::config::RadarIntrisic* mutable_intrinsics(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RadarIntrisic >*
      mutable_intrinsics();
  const ::esurfing::proto::config::RadarIntrisic& intrinsics(int index) const;
  ::esurfing::proto::config::RadarIntrisic* add_intrinsics();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RadarIntrisic >&
      intrinsics() const;

  // bytes name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const void* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // bytes type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  const ::std::string& type() const;
  void set_type(const ::std::string& value);
  #if LANG_CXX11
  void set_type(::std::string&& value);
  #endif
  void set_type(const char* value);
  void set_type(const void* value, size_t size);
  ::std::string* mutable_type();
  ::std::string* release_type();
  void set_allocated_type(::std::string* type);

  // bytes topic = 8;
  void clear_topic();
  static const int kTopicFieldNumber = 8;
  const ::std::string& topic() const;
  void set_topic(const ::std::string& value);
  #if LANG_CXX11
  void set_topic(::std::string&& value);
  #endif
  void set_topic(const char* value);
  void set_topic(const void* value, size_t size);
  ::std::string* mutable_topic();
  ::std::string* release_topic();
  void set_allocated_topic(::std::string* topic);

  // .esurfing.proto.math.Transformation3d tf_vehicle_radar = 6;
  bool has_tf_vehicle_radar() const;
  void clear_tf_vehicle_radar();
  static const int kTfVehicleRadarFieldNumber = 6;
  private:
  const ::esurfing::proto::math::Transformation3d& _internal_tf_vehicle_radar() const;
  public:
  const ::esurfing::proto::math::Transformation3d& tf_vehicle_radar() const;
  ::esurfing::proto::math::Transformation3d* release_tf_vehicle_radar();
  ::esurfing::proto::math::Transformation3d* mutable_tf_vehicle_radar();
  void set_allocated_tf_vehicle_radar(::esurfing::proto::math::Transformation3d* tf_vehicle_radar);

  // float min_distance = 4;
  void clear_min_distance();
  static const int kMinDistanceFieldNumber = 4;
  float min_distance() const;
  void set_min_distance(float value);

  // float max_distance = 5;
  void clear_max_distance();
  static const int kMaxDistanceFieldNumber = 5;
  float max_distance() const;
  void set_max_distance(float value);

  // float road_z = 7;
  void clear_road_z();
  static const int kRoadZFieldNumber = 7;
  float road_z() const;
  void set_road_z(float value);

  // int32 usage = 9;
  void clear_usage();
  static const int kUsageFieldNumber = 9;
  ::google::protobuf::int32 usage() const;
  void set_usage(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.Radar)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RadarIntrisic > intrinsics_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr type_;
  ::google::protobuf::internal::ArenaStringPtr topic_;
  ::esurfing::proto::math::Transformation3d* tf_vehicle_radar_;
  float min_distance_;
  float max_distance_;
  float road_z_;
  ::google::protobuf::int32 usage_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fradar_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ConfigRadars : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.config.ConfigRadars) */ {
 public:
  ConfigRadars();
  virtual ~ConfigRadars();

  ConfigRadars(const ConfigRadars& from);

  inline ConfigRadars& operator=(const ConfigRadars& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ConfigRadars(ConfigRadars&& from) noexcept
    : ConfigRadars() {
    *this = ::std::move(from);
  }

  inline ConfigRadars& operator=(ConfigRadars&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ConfigRadars& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConfigRadars* internal_default_instance() {
    return reinterpret_cast<const ConfigRadars*>(
               &_ConfigRadars_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(ConfigRadars* other);
  friend void swap(ConfigRadars& a, ConfigRadars& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ConfigRadars* New() const final {
    return CreateMaybeMessage<ConfigRadars>(NULL);
  }

  ConfigRadars* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ConfigRadars>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ConfigRadars& from);
  void MergeFrom(const ConfigRadars& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigRadars* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.config.Radar radars = 1;
  int radars_size() const;
  void clear_radars();
  static const int kRadarsFieldNumber = 1;
  ::esurfing::proto::config::Radar* mutable_radars(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >*
      mutable_radars();
  const ::esurfing::proto::config::Radar& radars(int index) const;
  ::esurfing::proto::config::Radar* add_radars();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >&
      radars() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.config.ConfigRadars)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar > radars_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fconfig_2fconfig_5fradar_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RadarIntrisic

// bool enabled = 1;
inline void RadarIntrisic::clear_enabled() {
  enabled_ = false;
}
inline bool RadarIntrisic::enabled() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.enabled)
  return enabled_;
}
inline void RadarIntrisic::set_enabled(bool value) {
  
  enabled_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.enabled)
}

// int32 min_intensity = 2;
inline void RadarIntrisic::clear_min_intensity() {
  min_intensity_ = 0;
}
inline ::google::protobuf::int32 RadarIntrisic::min_intensity() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.min_intensity)
  return min_intensity_;
}
inline void RadarIntrisic::set_min_intensity(::google::protobuf::int32 value) {
  
  min_intensity_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.min_intensity)
}

// int32 max_intensity = 3;
inline void RadarIntrisic::clear_max_intensity() {
  max_intensity_ = 0;
}
inline ::google::protobuf::int32 RadarIntrisic::max_intensity() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.max_intensity)
  return max_intensity_;
}
inline void RadarIntrisic::set_max_intensity(::google::protobuf::int32 value) {
  
  max_intensity_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.max_intensity)
}

// float rot = 4;
inline void RadarIntrisic::clear_rot() {
  rot_ = 0;
}
inline float RadarIntrisic::rot() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.rot)
  return rot_;
}
inline void RadarIntrisic::set_rot(float value) {
  
  rot_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.rot)
}

// float vert = 5;
inline void RadarIntrisic::clear_vert() {
  vert_ = 0;
}
inline float RadarIntrisic::vert() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.vert)
  return vert_;
}
inline void RadarIntrisic::set_vert(float value) {
  
  vert_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.vert)
}

// float dist = 6;
inline void RadarIntrisic::clear_dist() {
  dist_ = 0;
}
inline float RadarIntrisic::dist() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.dist)
  return dist_;
}
inline void RadarIntrisic::set_dist(float value) {
  
  dist_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.dist)
}

// float dist_x = 7;
inline void RadarIntrisic::clear_dist_x() {
  dist_x_ = 0;
}
inline float RadarIntrisic::dist_x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.dist_x)
  return dist_x_;
}
inline void RadarIntrisic::set_dist_x(float value) {
  
  dist_x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.dist_x)
}

// float dist_y = 8;
inline void RadarIntrisic::clear_dist_y() {
  dist_y_ = 0;
}
inline float RadarIntrisic::dist_y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.dist_y)
  return dist_y_;
}
inline void RadarIntrisic::set_dist_y(float value) {
  
  dist_y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.dist_y)
}

// float vert_offset = 9;
inline void RadarIntrisic::clear_vert_offset() {
  vert_offset_ = 0;
}
inline float RadarIntrisic::vert_offset() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.vert_offset)
  return vert_offset_;
}
inline void RadarIntrisic::set_vert_offset(float value) {
  
  vert_offset_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.vert_offset)
}

// float horiz_offset = 10;
inline void RadarIntrisic::clear_horiz_offset() {
  horiz_offset_ = 0;
}
inline float RadarIntrisic::horiz_offset() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.horiz_offset)
  return horiz_offset_;
}
inline void RadarIntrisic::set_horiz_offset(float value) {
  
  horiz_offset_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.horiz_offset)
}

// float focal_distance = 11;
inline void RadarIntrisic::clear_focal_distance() {
  focal_distance_ = 0;
}
inline float RadarIntrisic::focal_distance() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.focal_distance)
  return focal_distance_;
}
inline void RadarIntrisic::set_focal_distance(float value) {
  
  focal_distance_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.focal_distance)
}

// float focal_scope = 12;
inline void RadarIntrisic::clear_focal_scope() {
  focal_scope_ = 0;
}
inline float RadarIntrisic::focal_scope() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.focal_scope)
  return focal_scope_;
}
inline void RadarIntrisic::set_focal_scope(float value) {
  
  focal_scope_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.focal_scope)
}

// int32 ring = 13;
inline void RadarIntrisic::clear_ring() {
  ring_ = 0;
}
inline ::google::protobuf::int32 RadarIntrisic::ring() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.ring)
  return ring_;
}
inline void RadarIntrisic::set_ring(::google::protobuf::int32 value) {
  
  ring_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.ring)
}

// float cos_rot = 14;
inline void RadarIntrisic::clear_cos_rot() {
  cos_rot_ = 0;
}
inline float RadarIntrisic::cos_rot() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.cos_rot)
  return cos_rot_;
}
inline void RadarIntrisic::set_cos_rot(float value) {
  
  cos_rot_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.cos_rot)
}

// float sin_rot = 15;
inline void RadarIntrisic::clear_sin_rot() {
  sin_rot_ = 0;
}
inline float RadarIntrisic::sin_rot() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.sin_rot)
  return sin_rot_;
}
inline void RadarIntrisic::set_sin_rot(float value) {
  
  sin_rot_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.sin_rot)
}

// float cos_vert = 16;
inline void RadarIntrisic::clear_cos_vert() {
  cos_vert_ = 0;
}
inline float RadarIntrisic::cos_vert() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.cos_vert)
  return cos_vert_;
}
inline void RadarIntrisic::set_cos_vert(float value) {
  
  cos_vert_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.cos_vert)
}

// float sin_vert = 17;
inline void RadarIntrisic::clear_sin_vert() {
  sin_vert_ = 0;
}
inline float RadarIntrisic::sin_vert() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.RadarIntrisic.sin_vert)
  return sin_vert_;
}
inline void RadarIntrisic::set_sin_vert(float value) {
  
  sin_vert_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.RadarIntrisic.sin_vert)
}

// -------------------------------------------------------------------

// Radar

// bytes name = 1;
inline void Radar::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Radar::name() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.name)
  return name_.GetNoArena();
}
inline void Radar::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Radar.name)
}
#if LANG_CXX11
inline void Radar::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.Radar.name)
}
#endif
inline void Radar::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.Radar.name)
}
inline void Radar::set_name(const void* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.Radar.name)
}
inline ::std::string* Radar::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Radar.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Radar::release_name() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.Radar.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Radar::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.Radar.name)
}

// bytes type = 2;
inline void Radar::clear_type() {
  type_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Radar::type() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.type)
  return type_.GetNoArena();
}
inline void Radar::set_type(const ::std::string& value) {
  
  type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Radar.type)
}
#if LANG_CXX11
inline void Radar::set_type(::std::string&& value) {
  
  type_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.Radar.type)
}
#endif
inline void Radar::set_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.Radar.type)
}
inline void Radar::set_type(const void* value, size_t size) {
  
  type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.Radar.type)
}
inline ::std::string* Radar::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Radar.type)
  return type_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Radar::release_type() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.Radar.type)
  
  return type_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Radar::set_allocated_type(::std::string* type) {
  if (type != NULL) {
    
  } else {
    
  }
  type_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.Radar.type)
}

// repeated .esurfing.proto.config.RadarIntrisic intrinsics = 3;
inline int Radar::intrinsics_size() const {
  return intrinsics_.size();
}
inline void Radar::clear_intrinsics() {
  intrinsics_.Clear();
}
inline ::esurfing::proto::config::RadarIntrisic* Radar::mutable_intrinsics(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Radar.intrinsics)
  return intrinsics_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RadarIntrisic >*
Radar::mutable_intrinsics() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.Radar.intrinsics)
  return &intrinsics_;
}
inline const ::esurfing::proto::config::RadarIntrisic& Radar::intrinsics(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.intrinsics)
  return intrinsics_.Get(index);
}
inline ::esurfing::proto::config::RadarIntrisic* Radar::add_intrinsics() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.Radar.intrinsics)
  return intrinsics_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::RadarIntrisic >&
Radar::intrinsics() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.Radar.intrinsics)
  return intrinsics_;
}

// float min_distance = 4;
inline void Radar::clear_min_distance() {
  min_distance_ = 0;
}
inline float Radar::min_distance() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.min_distance)
  return min_distance_;
}
inline void Radar::set_min_distance(float value) {
  
  min_distance_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Radar.min_distance)
}

// float max_distance = 5;
inline void Radar::clear_max_distance() {
  max_distance_ = 0;
}
inline float Radar::max_distance() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.max_distance)
  return max_distance_;
}
inline void Radar::set_max_distance(float value) {
  
  max_distance_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Radar.max_distance)
}

// .esurfing.proto.math.Transformation3d tf_vehicle_radar = 6;
inline bool Radar::has_tf_vehicle_radar() const {
  return this != internal_default_instance() && tf_vehicle_radar_ != NULL;
}
inline const ::esurfing::proto::math::Transformation3d& Radar::_internal_tf_vehicle_radar() const {
  return *tf_vehicle_radar_;
}
inline const ::esurfing::proto::math::Transformation3d& Radar::tf_vehicle_radar() const {
  const ::esurfing::proto::math::Transformation3d* p = tf_vehicle_radar_;
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.tf_vehicle_radar)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Transformation3d*>(
      &::esurfing::proto::math::_Transformation3d_default_instance_);
}
inline ::esurfing::proto::math::Transformation3d* Radar::release_tf_vehicle_radar() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.Radar.tf_vehicle_radar)
  
  ::esurfing::proto::math::Transformation3d* temp = tf_vehicle_radar_;
  tf_vehicle_radar_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Transformation3d* Radar::mutable_tf_vehicle_radar() {
  
  if (tf_vehicle_radar_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Transformation3d>(GetArenaNoVirtual());
    tf_vehicle_radar_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Radar.tf_vehicle_radar)
  return tf_vehicle_radar_;
}
inline void Radar::set_allocated_tf_vehicle_radar(::esurfing::proto::math::Transformation3d* tf_vehicle_radar) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tf_vehicle_radar_);
  }
  if (tf_vehicle_radar) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      tf_vehicle_radar = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tf_vehicle_radar, submessage_arena);
    }
    
  } else {
    
  }
  tf_vehicle_radar_ = tf_vehicle_radar;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.Radar.tf_vehicle_radar)
}

// float road_z = 7;
inline void Radar::clear_road_z() {
  road_z_ = 0;
}
inline float Radar::road_z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.road_z)
  return road_z_;
}
inline void Radar::set_road_z(float value) {
  
  road_z_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Radar.road_z)
}

// bytes topic = 8;
inline void Radar::clear_topic() {
  topic_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Radar::topic() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.topic)
  return topic_.GetNoArena();
}
inline void Radar::set_topic(const ::std::string& value) {
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Radar.topic)
}
#if LANG_CXX11
inline void Radar::set_topic(::std::string&& value) {
  
  topic_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:esurfing.proto.config.Radar.topic)
}
#endif
inline void Radar::set_topic(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:esurfing.proto.config.Radar.topic)
}
inline void Radar::set_topic(const void* value, size_t size) {
  
  topic_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.config.Radar.topic)
}
inline ::std::string* Radar::mutable_topic() {
  
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.Radar.topic)
  return topic_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Radar::release_topic() {
  // @@protoc_insertion_point(field_release:esurfing.proto.config.Radar.topic)
  
  return topic_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Radar::set_allocated_topic(::std::string* topic) {
  if (topic != NULL) {
    
  } else {
    
  }
  topic_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), topic);
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.config.Radar.topic)
}

// int32 usage = 9;
inline void Radar::clear_usage() {
  usage_ = 0;
}
inline ::google::protobuf::int32 Radar::usage() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.Radar.usage)
  return usage_;
}
inline void Radar::set_usage(::google::protobuf::int32 value) {
  
  usage_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.config.Radar.usage)
}

// -------------------------------------------------------------------

// ConfigRadars

// repeated .esurfing.proto.config.Radar radars = 1;
inline int ConfigRadars::radars_size() const {
  return radars_.size();
}
inline void ConfigRadars::clear_radars() {
  radars_.Clear();
}
inline ::esurfing::proto::config::Radar* ConfigRadars::mutable_radars(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.config.ConfigRadars.radars)
  return radars_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >*
ConfigRadars::mutable_radars() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.config.ConfigRadars.radars)
  return &radars_;
}
inline const ::esurfing::proto::config::Radar& ConfigRadars::radars(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.config.ConfigRadars.radars)
  return radars_.Get(index);
}
inline ::esurfing::proto::config::Radar* ConfigRadars::add_radars() {
  // @@protoc_insertion_point(field_add:esurfing.proto.config.ConfigRadars.radars)
  return radars_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::config::Radar >&
ConfigRadars::radars() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.config.ConfigRadars.radars)
  return radars_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace config
}  // namespace proto
}  // namespace esurfing

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_proto_2fconfig_2fconfig_5fradar_2eproto
