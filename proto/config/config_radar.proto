// Copyright
// Author:

syntax = "proto3";

package esurfing.proto.config;

import "proto/math/geo.proto";

option go_package = "proto/config/config_radar";
option java_package = "proto.config";
option java_outer_classname = "RadarConfig";
option java_multiple_files = false;

message RadarIntrisic {
  bool enabled = 1;
  int32 min_intensity = 2;
  int32 max_intensity = 3;

  float rot = 4;
  float vert = 5;
  float dist = 6;
  float dist_x = 7;
  float dist_y = 8;
  float vert_offset = 9;
  float horiz_offset = 10;
  float focal_distance = 11;
  float focal_scope = 12;
  int32 ring = 13;
  float cos_rot = 14;
  float sin_rot = 15;
  float cos_vert = 16;
  float sin_vert = 17;
}

message Radar {
  bytes name = 1;
  bytes type = 2;
  repeated RadarIntrisic intrinsics = 3;

  float min_distance = 4;
  float max_distance = 5;

  math.Transformation3d tf_vehicle_radar = 6;

  float road_z = 7;
  bytes topic = 8;
  int32 usage = 9;
}

message ConfigRadars {
  repeated Radar radars = 1;
}
