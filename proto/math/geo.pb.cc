// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/math/geo.proto

#include "proto/math/geo.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_proto_2fmath_2fgeo_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Quaterniond;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Quaternionf;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Vector2f;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Vector3d;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Vector3f;
}  // namespace protobuf_proto_2fmath_2fgeo_2eproto
namespace esurfing {
namespace proto {
namespace math {
class Vector3iDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Vector3i>
      _instance;
} _Vector3i_default_instance_;
class PolylineDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Polyline>
      _instance;
} _Polyline_default_instance_;
class PolygonDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Polygon>
      _instance;
} _Polygon_default_instance_;
class PointsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Points>
      _instance;
} _Points_default_instance_;
class Vector2dDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Vector2d>
      _instance;
} _Vector2d_default_instance_;
class Vector2fDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Vector2f>
      _instance;
} _Vector2f_default_instance_;
class Vector3dDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Vector3d>
      _instance;
} _Vector3d_default_instance_;
class Vector3fDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Vector3f>
      _instance;
} _Vector3f_default_instance_;
class Matrix2dDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Matrix2d>
      _instance;
} _Matrix2d_default_instance_;
class Matrix2fDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Matrix2f>
      _instance;
} _Matrix2f_default_instance_;
class Matrix3dDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Matrix3d>
      _instance;
} _Matrix3d_default_instance_;
class Matrix3fDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Matrix3f>
      _instance;
} _Matrix3f_default_instance_;
class QuaterniondDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Quaterniond>
      _instance;
} _Quaterniond_default_instance_;
class QuaternionfDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Quaternionf>
      _instance;
} _Quaternionf_default_instance_;
class Transformation3dDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Transformation3d>
      _instance;
} _Transformation3d_default_instance_;
class Transformation3fDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Transformation3f>
      _instance;
} _Transformation3f_default_instance_;
}  // namespace math
}  // namespace proto
}  // namespace esurfing
namespace protobuf_proto_2fmath_2fgeo_2eproto {
static void InitDefaultsVector3i() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Vector3i_default_instance_;
    new (ptr) ::esurfing::proto::math::Vector3i();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Vector3i::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Vector3i =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVector3i}, {}};

static void InitDefaultsPolyline() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Polyline_default_instance_;
    new (ptr) ::esurfing::proto::math::Polyline();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Polyline::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Polyline =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsPolyline}, {
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3d.base,}};

static void InitDefaultsPolygon() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Polygon_default_instance_;
    new (ptr) ::esurfing::proto::math::Polygon();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Polygon::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Polygon =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsPolygon}, {
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector2f.base,}};

static void InitDefaultsPoints() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Points_default_instance_;
    new (ptr) ::esurfing::proto::math::Points();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Points::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Points =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsPoints}, {
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3d.base,}};

static void InitDefaultsVector2d() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Vector2d_default_instance_;
    new (ptr) ::esurfing::proto::math::Vector2d();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Vector2d::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Vector2d =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVector2d}, {}};

static void InitDefaultsVector2f() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Vector2f_default_instance_;
    new (ptr) ::esurfing::proto::math::Vector2f();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Vector2f::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Vector2f =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVector2f}, {}};

static void InitDefaultsVector3d() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Vector3d_default_instance_;
    new (ptr) ::esurfing::proto::math::Vector3d();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Vector3d::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Vector3d =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVector3d}, {}};

static void InitDefaultsVector3f() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Vector3f_default_instance_;
    new (ptr) ::esurfing::proto::math::Vector3f();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Vector3f::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Vector3f =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVector3f}, {}};

static void InitDefaultsMatrix2d() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Matrix2d_default_instance_;
    new (ptr) ::esurfing::proto::math::Matrix2d();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Matrix2d::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Matrix2d =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMatrix2d}, {}};

static void InitDefaultsMatrix2f() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Matrix2f_default_instance_;
    new (ptr) ::esurfing::proto::math::Matrix2f();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Matrix2f::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Matrix2f =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMatrix2f}, {}};

static void InitDefaultsMatrix3d() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Matrix3d_default_instance_;
    new (ptr) ::esurfing::proto::math::Matrix3d();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Matrix3d::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Matrix3d =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMatrix3d}, {}};

static void InitDefaultsMatrix3f() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Matrix3f_default_instance_;
    new (ptr) ::esurfing::proto::math::Matrix3f();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Matrix3f::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Matrix3f =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMatrix3f}, {}};

static void InitDefaultsQuaterniond() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Quaterniond_default_instance_;
    new (ptr) ::esurfing::proto::math::Quaterniond();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Quaterniond::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Quaterniond =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsQuaterniond}, {}};

static void InitDefaultsQuaternionf() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Quaternionf_default_instance_;
    new (ptr) ::esurfing::proto::math::Quaternionf();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Quaternionf::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Quaternionf =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsQuaternionf}, {}};

static void InitDefaultsTransformation3d() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Transformation3d_default_instance_;
    new (ptr) ::esurfing::proto::math::Transformation3d();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Transformation3d::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_Transformation3d =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsTransformation3d}, {
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Quaterniond.base,
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3d.base,}};

static void InitDefaultsTransformation3f() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::esurfing::proto::math::_Transformation3f_default_instance_;
    new (ptr) ::esurfing::proto::math::Transformation3f();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::esurfing::proto::math::Transformation3f::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_Transformation3f =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsTransformation3f}, {
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Quaternionf.base,
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3f.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_Vector3i.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Polyline.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Polygon.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Points.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Vector2d.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Vector2f.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Vector3d.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Vector3f.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Matrix2d.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Matrix2f.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Matrix3d.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Matrix3f.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Quaterniond.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Quaternionf.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Transformation3d.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Transformation3f.base);
}

::google::protobuf::Metadata file_level_metadata[16];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3i, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3i, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3i, y_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3i, z_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Polyline, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Polyline, points_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Polygon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Polygon, points_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Points, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Points, points_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector2d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector2d, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector2d, y_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector2f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector2f, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector2f, y_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3d, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3d, y_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3d, z_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3f, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3f, y_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Vector3f, z_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, e00_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, e01_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, e10_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, e11_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, e00_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, e01_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, e10_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, e11_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e00_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e01_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e02_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e10_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e11_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e12_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e20_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e21_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, e22_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e00_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e01_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e02_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e10_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e11_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e12_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e20_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e21_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, e22_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, w_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, y_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, z_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, w_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, y_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, z_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Transformation3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Transformation3d, rotation_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Transformation3d, translation_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Transformation3f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Transformation3f, rotation_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::esurfing::proto::math::Transformation3f, translation_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::esurfing::proto::math::Vector3i)},
  { 8, -1, sizeof(::esurfing::proto::math::Polyline)},
  { 14, -1, sizeof(::esurfing::proto::math::Polygon)},
  { 20, -1, sizeof(::esurfing::proto::math::Points)},
  { 26, -1, sizeof(::esurfing::proto::math::Vector2d)},
  { 33, -1, sizeof(::esurfing::proto::math::Vector2f)},
  { 40, -1, sizeof(::esurfing::proto::math::Vector3d)},
  { 48, -1, sizeof(::esurfing::proto::math::Vector3f)},
  { 56, -1, sizeof(::esurfing::proto::math::Matrix2d)},
  { 65, -1, sizeof(::esurfing::proto::math::Matrix2f)},
  { 74, -1, sizeof(::esurfing::proto::math::Matrix3d)},
  { 88, -1, sizeof(::esurfing::proto::math::Matrix3f)},
  { 102, -1, sizeof(::esurfing::proto::math::Quaterniond)},
  { 111, -1, sizeof(::esurfing::proto::math::Quaternionf)},
  { 120, -1, sizeof(::esurfing::proto::math::Transformation3d)},
  { 127, -1, sizeof(::esurfing::proto::math::Transformation3f)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Vector3i_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Polyline_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Polygon_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Points_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Vector2d_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Vector2f_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Vector3d_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Vector3f_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Matrix2d_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Matrix2f_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Matrix3d_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Matrix3f_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Quaterniond_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Quaternionf_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Transformation3d_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::esurfing::proto::math::_Transformation3f_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "proto/math/geo.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 16);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\024proto/math/geo.proto\022\023esurfing.proto.m"
      "ath\"+\n\010Vector3i\022\t\n\001x\030\001 \001(\003\022\t\n\001y\030\002 \001(\003\022\t\n"
      "\001z\030\003 \001(\003\"9\n\010Polyline\022-\n\006points\030\001 \003(\0132\035.e"
      "surfing.proto.math.Vector3d\"8\n\007Polygon\022-"
      "\n\006points\030\001 \003(\0132\035.esurfing.proto.math.Vec"
      "tor2f\"7\n\006Points\022-\n\006points\030\001 \003(\0132\035.esurfi"
      "ng.proto.math.Vector3d\" \n\010Vector2d\022\t\n\001x\030"
      "\001 \001(\001\022\t\n\001y\030\002 \001(\001\" \n\010Vector2f\022\t\n\001x\030\001 \001(\002\022"
      "\t\n\001y\030\002 \001(\002\"+\n\010Vector3d\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002"
      " \001(\001\022\t\n\001z\030\003 \001(\001\"+\n\010Vector3f\022\t\n\001x\030\001 \001(\002\022\t"
      "\n\001y\030\002 \001(\002\022\t\n\001z\030\003 \001(\002\">\n\010Matrix2d\022\013\n\003e00\030"
      "\001 \001(\001\022\013\n\003e01\030\002 \001(\001\022\013\n\003e10\030\003 \001(\001\022\013\n\003e11\030\004"
      " \001(\001\">\n\010Matrix2f\022\013\n\003e00\030\001 \001(\002\022\013\n\003e01\030\002 \001"
      "(\002\022\013\n\003e10\030\003 \001(\002\022\013\n\003e11\030\004 \001(\002\"\177\n\010Matrix3d"
      "\022\013\n\003e00\030\001 \001(\001\022\013\n\003e01\030\002 \001(\001\022\013\n\003e02\030\003 \001(\001\022"
      "\013\n\003e10\030\004 \001(\001\022\013\n\003e11\030\005 \001(\001\022\013\n\003e12\030\006 \001(\001\022\013"
      "\n\003e20\030\007 \001(\001\022\013\n\003e21\030\010 \001(\001\022\013\n\003e22\030\t \001(\001\"\177\n"
      "\010Matrix3f\022\013\n\003e00\030\001 \001(\002\022\013\n\003e01\030\002 \001(\002\022\013\n\003e"
      "02\030\003 \001(\002\022\013\n\003e10\030\004 \001(\002\022\013\n\003e11\030\005 \001(\002\022\013\n\003e1"
      "2\030\006 \001(\002\022\013\n\003e20\030\007 \001(\002\022\013\n\003e21\030\010 \001(\002\022\013\n\003e22"
      "\030\t \001(\002\"9\n\013Quaterniond\022\t\n\001w\030\001 \001(\001\022\t\n\001x\030\002 "
      "\001(\001\022\t\n\001y\030\003 \001(\001\022\t\n\001z\030\004 \001(\001\"9\n\013Quaternionf"
      "\022\t\n\001w\030\001 \001(\002\022\t\n\001x\030\002 \001(\002\022\t\n\001y\030\003 \001(\002\022\t\n\001z\030\004"
      " \001(\002\"z\n\020Transformation3d\0222\n\010rotation\030\001 \001"
      "(\0132 .esurfing.proto.math.Quaterniond\0222\n\013"
      "translation\030\002 \001(\0132\035.esurfing.proto.math."
      "Vector3d\"z\n\020Transformation3f\0222\n\010rotation"
      "\030\001 \001(\0132 .esurfing.proto.math.Quaternionf"
      "\0222\n\013translation\030\002 \001(\0132\035.esurfing.proto.m"
      "ath.Vector3fB(\n\nproto.mathB\003GeoP\000Z\023proto"
      "/math/geometryb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1222);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "proto/math/geo.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_proto_2fmath_2fgeo_2eproto
namespace esurfing {
namespace proto {
namespace math {

// ===================================================================

void Vector3i::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Vector3i::kXFieldNumber;
const int Vector3i::kYFieldNumber;
const int Vector3i::kZFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Vector3i::Vector3i()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3i.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Vector3i)
}
Vector3i::Vector3i(const Vector3i& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector3i)
}

void Vector3i::SharedCtor() {
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

Vector3i::~Vector3i() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector3i)
  SharedDtor();
}

void Vector3i::SharedDtor() {
}

void Vector3i::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Vector3i::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Vector3i& Vector3i::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3i.base);
  return *internal_default_instance();
}


void Vector3i::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector3i)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear();
}

bool Vector3i::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Vector3i)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 x = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 y = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 z = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &z_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Vector3i)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Vector3i)
  return false;
#undef DO_
}

void Vector3i::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Vector3i)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 x = 1;
  if (this->x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->x(), output);
  }

  // int64 y = 2;
  if (this->y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->y(), output);
  }

  // int64 z = 3;
  if (this->z() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->z(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Vector3i)
}

::google::protobuf::uint8* Vector3i::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector3i)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 x = 1;
  if (this->x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->x(), target);
  }

  // int64 y = 2;
  if (this->y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->y(), target);
  }

  // int64 z = 3;
  if (this->z() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->z(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector3i)
  return target;
}

size_t Vector3i::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector3i)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 x = 1;
  if (this->x() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->x());
  }

  // int64 y = 2;
  if (this->y() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->y());
  }

  // int64 z = 3;
  if (this->z() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->z());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Vector3i::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Vector3i)
  GOOGLE_DCHECK_NE(&from, this);
  const Vector3i* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Vector3i>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Vector3i)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Vector3i)
    MergeFrom(*source);
  }
}

void Vector3i::MergeFrom(const Vector3i& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector3i)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.x() != 0) {
    set_x(from.x());
  }
  if (from.y() != 0) {
    set_y(from.y());
  }
  if (from.z() != 0) {
    set_z(from.z());
  }
}

void Vector3i::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Vector3i)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Vector3i::CopyFrom(const Vector3i& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector3i)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3i::IsInitialized() const {
  return true;
}

void Vector3i::Swap(Vector3i* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Vector3i::InternalSwap(Vector3i* other) {
  using std::swap;
  swap(x_, other->x_);
  swap(y_, other->y_);
  swap(z_, other->z_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Vector3i::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Polyline::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Polyline::kPointsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Polyline::Polyline()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Polyline.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Polyline)
}
Polyline::Polyline(const Polyline& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      points_(from.points_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Polyline)
}

void Polyline::SharedCtor() {
}

Polyline::~Polyline() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Polyline)
  SharedDtor();
}

void Polyline::SharedDtor() {
}

void Polyline::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Polyline::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Polyline& Polyline::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Polyline.base);
  return *internal_default_instance();
}


void Polyline::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Polyline)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  _internal_metadata_.Clear();
}

bool Polyline::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Polyline)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .esurfing.proto.math.Vector3d points = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_points()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Polyline)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Polyline)
  return false;
#undef DO_
}

void Polyline::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Polyline)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->points(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Polyline)
}

::google::protobuf::uint8* Polyline::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Polyline)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->points(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Polyline)
  return target;
}

size_t Polyline::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Polyline)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.math.Vector3d points = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->points_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->points(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Polyline::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Polyline)
  GOOGLE_DCHECK_NE(&from, this);
  const Polyline* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Polyline>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Polyline)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Polyline)
    MergeFrom(*source);
  }
}

void Polyline::MergeFrom(const Polyline& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Polyline)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
}

void Polyline::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Polyline)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Polyline::CopyFrom(const Polyline& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Polyline)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polyline::IsInitialized() const {
  return true;
}

void Polyline::Swap(Polyline* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Polyline::InternalSwap(Polyline* other) {
  using std::swap;
  CastToBase(&points_)->InternalSwap(CastToBase(&other->points_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Polyline::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Polygon::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Polygon::kPointsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Polygon::Polygon()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Polygon.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Polygon)
}
Polygon::Polygon(const Polygon& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      points_(from.points_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Polygon)
}

void Polygon::SharedCtor() {
}

Polygon::~Polygon() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Polygon)
  SharedDtor();
}

void Polygon::SharedDtor() {
}

void Polygon::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Polygon::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Polygon& Polygon::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Polygon.base);
  return *internal_default_instance();
}


void Polygon::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Polygon)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  _internal_metadata_.Clear();
}

bool Polygon::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Polygon)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .esurfing.proto.math.Vector2f points = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_points()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Polygon)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Polygon)
  return false;
#undef DO_
}

void Polygon::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Polygon)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector2f points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->points(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Polygon)
}

::google::protobuf::uint8* Polygon::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Polygon)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector2f points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->points(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Polygon)
  return target;
}

size_t Polygon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Polygon)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.math.Vector2f points = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->points_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->points(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Polygon::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Polygon)
  GOOGLE_DCHECK_NE(&from, this);
  const Polygon* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Polygon>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Polygon)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Polygon)
    MergeFrom(*source);
  }
}

void Polygon::MergeFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Polygon)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
}

void Polygon::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Polygon::CopyFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polygon::IsInitialized() const {
  return true;
}

void Polygon::Swap(Polygon* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Polygon::InternalSwap(Polygon* other) {
  using std::swap;
  CastToBase(&points_)->InternalSwap(CastToBase(&other->points_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Polygon::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Points::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Points::kPointsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Points::Points()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Points.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Points)
}
Points::Points(const Points& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      points_(from.points_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Points)
}

void Points::SharedCtor() {
}

Points::~Points() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Points)
  SharedDtor();
}

void Points::SharedDtor() {
}

void Points::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Points::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Points& Points::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Points.base);
  return *internal_default_instance();
}


void Points::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Points)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  _internal_metadata_.Clear();
}

bool Points::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Points)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .esurfing.proto.math.Vector3d points = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_points()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Points)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Points)
  return false;
#undef DO_
}

void Points::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Points)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->points(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Points)
}

::google::protobuf::uint8* Points::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Points)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->points(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Points)
  return target;
}

size_t Points::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Points)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .esurfing.proto.math.Vector3d points = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->points_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->points(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Points::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Points)
  GOOGLE_DCHECK_NE(&from, this);
  const Points* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Points>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Points)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Points)
    MergeFrom(*source);
  }
}

void Points::MergeFrom(const Points& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Points)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
}

void Points::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Points)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Points::CopyFrom(const Points& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Points)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Points::IsInitialized() const {
  return true;
}

void Points::Swap(Points* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Points::InternalSwap(Points* other) {
  using std::swap;
  CastToBase(&points_)->InternalSwap(CastToBase(&other->points_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Points::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Vector2d::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Vector2d::kXFieldNumber;
const int Vector2d::kYFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Vector2d::Vector2d()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector2d.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Vector2d)
}
Vector2d::Vector2d(const Vector2d& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector2d)
}

void Vector2d::SharedCtor() {
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
}

Vector2d::~Vector2d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector2d)
  SharedDtor();
}

void Vector2d::SharedDtor() {
}

void Vector2d::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Vector2d::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Vector2d& Vector2d::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector2d.base);
  return *internal_default_instance();
}


void Vector2d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector2d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
  _internal_metadata_.Clear();
}

bool Vector2d::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Vector2d)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double x = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double y = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Vector2d)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Vector2d)
  return false;
#undef DO_
}

void Vector2d::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Vector2d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (this->x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->x(), output);
  }

  // double y = 2;
  if (this->y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->y(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Vector2d)
}

::google::protobuf::uint8* Vector2d::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector2d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (this->x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->x(), target);
  }

  // double y = 2;
  if (this->y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->y(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector2d)
  return target;
}

size_t Vector2d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector2d)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double x = 1;
  if (this->x() != 0) {
    total_size += 1 + 8;
  }

  // double y = 2;
  if (this->y() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Vector2d::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Vector2d)
  GOOGLE_DCHECK_NE(&from, this);
  const Vector2d* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Vector2d>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Vector2d)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Vector2d)
    MergeFrom(*source);
  }
}

void Vector2d::MergeFrom(const Vector2d& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector2d)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.x() != 0) {
    set_x(from.x());
  }
  if (from.y() != 0) {
    set_y(from.y());
  }
}

void Vector2d::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Vector2d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Vector2d::CopyFrom(const Vector2d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector2d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector2d::IsInitialized() const {
  return true;
}

void Vector2d::Swap(Vector2d* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Vector2d::InternalSwap(Vector2d* other) {
  using std::swap;
  swap(x_, other->x_);
  swap(y_, other->y_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Vector2d::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Vector2f::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Vector2f::kXFieldNumber;
const int Vector2f::kYFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Vector2f::Vector2f()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector2f.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Vector2f)
}
Vector2f::Vector2f(const Vector2f& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector2f)
}

void Vector2f::SharedCtor() {
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
}

Vector2f::~Vector2f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector2f)
  SharedDtor();
}

void Vector2f::SharedDtor() {
}

void Vector2f::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Vector2f::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Vector2f& Vector2f::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector2f.base);
  return *internal_default_instance();
}


void Vector2f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector2f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
  _internal_metadata_.Clear();
}

bool Vector2f::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Vector2f)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float x = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float y = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Vector2f)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Vector2f)
  return false;
#undef DO_
}

void Vector2f::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Vector2f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  if (this->x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->x(), output);
  }

  // float y = 2;
  if (this->y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->y(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Vector2f)
}

::google::protobuf::uint8* Vector2f::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector2f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  if (this->x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->x(), target);
  }

  // float y = 2;
  if (this->y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->y(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector2f)
  return target;
}

size_t Vector2f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector2f)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float x = 1;
  if (this->x() != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  if (this->y() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Vector2f::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Vector2f)
  GOOGLE_DCHECK_NE(&from, this);
  const Vector2f* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Vector2f>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Vector2f)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Vector2f)
    MergeFrom(*source);
  }
}

void Vector2f::MergeFrom(const Vector2f& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector2f)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.x() != 0) {
    set_x(from.x());
  }
  if (from.y() != 0) {
    set_y(from.y());
  }
}

void Vector2f::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Vector2f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Vector2f::CopyFrom(const Vector2f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector2f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector2f::IsInitialized() const {
  return true;
}

void Vector2f::Swap(Vector2f* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Vector2f::InternalSwap(Vector2f* other) {
  using std::swap;
  swap(x_, other->x_);
  swap(y_, other->y_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Vector2f::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Vector3d::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Vector3d::kXFieldNumber;
const int Vector3d::kYFieldNumber;
const int Vector3d::kZFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Vector3d::Vector3d()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3d.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Vector3d)
}
Vector3d::Vector3d(const Vector3d& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector3d)
}

void Vector3d::SharedCtor() {
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

Vector3d::~Vector3d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector3d)
  SharedDtor();
}

void Vector3d::SharedDtor() {
}

void Vector3d::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Vector3d::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Vector3d& Vector3d::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3d.base);
  return *internal_default_instance();
}


void Vector3d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear();
}

bool Vector3d::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Vector3d)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double x = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double y = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double z = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &z_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Vector3d)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Vector3d)
  return false;
#undef DO_
}

void Vector3d::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Vector3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (this->x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->x(), output);
  }

  // double y = 2;
  if (this->y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->y(), output);
  }

  // double z = 3;
  if (this->z() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->z(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Vector3d)
}

::google::protobuf::uint8* Vector3d::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (this->x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->x(), target);
  }

  // double y = 2;
  if (this->y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->y(), target);
  }

  // double z = 3;
  if (this->z() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->z(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector3d)
  return target;
}

size_t Vector3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector3d)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double x = 1;
  if (this->x() != 0) {
    total_size += 1 + 8;
  }

  // double y = 2;
  if (this->y() != 0) {
    total_size += 1 + 8;
  }

  // double z = 3;
  if (this->z() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Vector3d::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Vector3d)
  GOOGLE_DCHECK_NE(&from, this);
  const Vector3d* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Vector3d>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Vector3d)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Vector3d)
    MergeFrom(*source);
  }
}

void Vector3d::MergeFrom(const Vector3d& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector3d)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.x() != 0) {
    set_x(from.x());
  }
  if (from.y() != 0) {
    set_y(from.y());
  }
  if (from.z() != 0) {
    set_z(from.z());
  }
}

void Vector3d::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Vector3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Vector3d::CopyFrom(const Vector3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3d::IsInitialized() const {
  return true;
}

void Vector3d::Swap(Vector3d* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Vector3d::InternalSwap(Vector3d* other) {
  using std::swap;
  swap(x_, other->x_);
  swap(y_, other->y_);
  swap(z_, other->z_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Vector3d::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Vector3f::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Vector3f::kXFieldNumber;
const int Vector3f::kYFieldNumber;
const int Vector3f::kZFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Vector3f::Vector3f()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3f.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Vector3f)
}
Vector3f::Vector3f(const Vector3f& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector3f)
}

void Vector3f::SharedCtor() {
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

Vector3f::~Vector3f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector3f)
  SharedDtor();
}

void Vector3f::SharedDtor() {
}

void Vector3f::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Vector3f::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Vector3f& Vector3f::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Vector3f.base);
  return *internal_default_instance();
}


void Vector3f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear();
}

bool Vector3f::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Vector3f)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float x = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float y = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float z = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &z_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Vector3f)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Vector3f)
  return false;
#undef DO_
}

void Vector3f::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Vector3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  if (this->x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->x(), output);
  }

  // float y = 2;
  if (this->y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->y(), output);
  }

  // float z = 3;
  if (this->z() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->z(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Vector3f)
}

::google::protobuf::uint8* Vector3f::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  if (this->x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->x(), target);
  }

  // float y = 2;
  if (this->y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->y(), target);
  }

  // float z = 3;
  if (this->z() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->z(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector3f)
  return target;
}

size_t Vector3f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector3f)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float x = 1;
  if (this->x() != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  if (this->y() != 0) {
    total_size += 1 + 4;
  }

  // float z = 3;
  if (this->z() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Vector3f::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Vector3f)
  GOOGLE_DCHECK_NE(&from, this);
  const Vector3f* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Vector3f>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Vector3f)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Vector3f)
    MergeFrom(*source);
  }
}

void Vector3f::MergeFrom(const Vector3f& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector3f)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.x() != 0) {
    set_x(from.x());
  }
  if (from.y() != 0) {
    set_y(from.y());
  }
  if (from.z() != 0) {
    set_z(from.z());
  }
}

void Vector3f::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Vector3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Vector3f::CopyFrom(const Vector3f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3f::IsInitialized() const {
  return true;
}

void Vector3f::Swap(Vector3f* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Vector3f::InternalSwap(Vector3f* other) {
  using std::swap;
  swap(x_, other->x_);
  swap(y_, other->y_);
  swap(z_, other->z_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Vector3f::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Matrix2d::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Matrix2d::kE00FieldNumber;
const int Matrix2d::kE01FieldNumber;
const int Matrix2d::kE10FieldNumber;
const int Matrix2d::kE11FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Matrix2d::Matrix2d()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix2d.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Matrix2d)
}
Matrix2d::Matrix2d(const Matrix2d& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&e00_, &from.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&e11_) -
    reinterpret_cast<char*>(&e00_)) + sizeof(e11_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix2d)
}

void Matrix2d::SharedCtor() {
  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e11_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e11_));
}

Matrix2d::~Matrix2d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix2d)
  SharedDtor();
}

void Matrix2d::SharedDtor() {
}

void Matrix2d::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Matrix2d::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Matrix2d& Matrix2d::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix2d.base);
  return *internal_default_instance();
}


void Matrix2d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix2d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e11_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e11_));
  _internal_metadata_.Clear();
}

bool Matrix2d::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Matrix2d)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double e00 = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e00_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e01 = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e01_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e10 = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e10_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e11 = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e11_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Matrix2d)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Matrix2d)
  return false;
#undef DO_
}

void Matrix2d::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Matrix2d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double e00 = 1;
  if (this->e00() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->e00(), output);
  }

  // double e01 = 2;
  if (this->e01() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->e01(), output);
  }

  // double e10 = 3;
  if (this->e10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->e10(), output);
  }

  // double e11 = 4;
  if (this->e11() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->e11(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Matrix2d)
}

::google::protobuf::uint8* Matrix2d::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix2d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double e00 = 1;
  if (this->e00() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->e00(), target);
  }

  // double e01 = 2;
  if (this->e01() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->e01(), target);
  }

  // double e10 = 3;
  if (this->e10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->e10(), target);
  }

  // double e11 = 4;
  if (this->e11() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->e11(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix2d)
  return target;
}

size_t Matrix2d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix2d)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double e00 = 1;
  if (this->e00() != 0) {
    total_size += 1 + 8;
  }

  // double e01 = 2;
  if (this->e01() != 0) {
    total_size += 1 + 8;
  }

  // double e10 = 3;
  if (this->e10() != 0) {
    total_size += 1 + 8;
  }

  // double e11 = 4;
  if (this->e11() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Matrix2d::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Matrix2d)
  GOOGLE_DCHECK_NE(&from, this);
  const Matrix2d* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Matrix2d>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Matrix2d)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Matrix2d)
    MergeFrom(*source);
  }
}

void Matrix2d::MergeFrom(const Matrix2d& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix2d)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.e00() != 0) {
    set_e00(from.e00());
  }
  if (from.e01() != 0) {
    set_e01(from.e01());
  }
  if (from.e10() != 0) {
    set_e10(from.e10());
  }
  if (from.e11() != 0) {
    set_e11(from.e11());
  }
}

void Matrix2d::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Matrix2d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Matrix2d::CopyFrom(const Matrix2d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix2d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix2d::IsInitialized() const {
  return true;
}

void Matrix2d::Swap(Matrix2d* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Matrix2d::InternalSwap(Matrix2d* other) {
  using std::swap;
  swap(e00_, other->e00_);
  swap(e01_, other->e01_);
  swap(e10_, other->e10_);
  swap(e11_, other->e11_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Matrix2d::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Matrix2f::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Matrix2f::kE00FieldNumber;
const int Matrix2f::kE01FieldNumber;
const int Matrix2f::kE10FieldNumber;
const int Matrix2f::kE11FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Matrix2f::Matrix2f()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix2f.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Matrix2f)
}
Matrix2f::Matrix2f(const Matrix2f& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&e00_, &from.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&e11_) -
    reinterpret_cast<char*>(&e00_)) + sizeof(e11_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix2f)
}

void Matrix2f::SharedCtor() {
  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e11_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e11_));
}

Matrix2f::~Matrix2f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix2f)
  SharedDtor();
}

void Matrix2f::SharedDtor() {
}

void Matrix2f::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Matrix2f::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Matrix2f& Matrix2f::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix2f.base);
  return *internal_default_instance();
}


void Matrix2f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix2f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e11_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e11_));
  _internal_metadata_.Clear();
}

bool Matrix2f::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Matrix2f)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float e00 = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e00_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e01 = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e01_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e10 = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e10_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e11 = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e11_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Matrix2f)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Matrix2f)
  return false;
#undef DO_
}

void Matrix2f::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Matrix2f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float e00 = 1;
  if (this->e00() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->e00(), output);
  }

  // float e01 = 2;
  if (this->e01() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->e01(), output);
  }

  // float e10 = 3;
  if (this->e10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->e10(), output);
  }

  // float e11 = 4;
  if (this->e11() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->e11(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Matrix2f)
}

::google::protobuf::uint8* Matrix2f::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix2f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float e00 = 1;
  if (this->e00() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->e00(), target);
  }

  // float e01 = 2;
  if (this->e01() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->e01(), target);
  }

  // float e10 = 3;
  if (this->e10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->e10(), target);
  }

  // float e11 = 4;
  if (this->e11() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->e11(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix2f)
  return target;
}

size_t Matrix2f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix2f)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float e00 = 1;
  if (this->e00() != 0) {
    total_size += 1 + 4;
  }

  // float e01 = 2;
  if (this->e01() != 0) {
    total_size += 1 + 4;
  }

  // float e10 = 3;
  if (this->e10() != 0) {
    total_size += 1 + 4;
  }

  // float e11 = 4;
  if (this->e11() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Matrix2f::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Matrix2f)
  GOOGLE_DCHECK_NE(&from, this);
  const Matrix2f* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Matrix2f>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Matrix2f)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Matrix2f)
    MergeFrom(*source);
  }
}

void Matrix2f::MergeFrom(const Matrix2f& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix2f)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.e00() != 0) {
    set_e00(from.e00());
  }
  if (from.e01() != 0) {
    set_e01(from.e01());
  }
  if (from.e10() != 0) {
    set_e10(from.e10());
  }
  if (from.e11() != 0) {
    set_e11(from.e11());
  }
}

void Matrix2f::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Matrix2f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Matrix2f::CopyFrom(const Matrix2f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix2f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix2f::IsInitialized() const {
  return true;
}

void Matrix2f::Swap(Matrix2f* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Matrix2f::InternalSwap(Matrix2f* other) {
  using std::swap;
  swap(e00_, other->e00_);
  swap(e01_, other->e01_);
  swap(e10_, other->e10_);
  swap(e11_, other->e11_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Matrix2f::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Matrix3d::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Matrix3d::kE00FieldNumber;
const int Matrix3d::kE01FieldNumber;
const int Matrix3d::kE02FieldNumber;
const int Matrix3d::kE10FieldNumber;
const int Matrix3d::kE11FieldNumber;
const int Matrix3d::kE12FieldNumber;
const int Matrix3d::kE20FieldNumber;
const int Matrix3d::kE21FieldNumber;
const int Matrix3d::kE22FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Matrix3d::Matrix3d()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix3d.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Matrix3d)
}
Matrix3d::Matrix3d(const Matrix3d& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&e00_, &from.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&e22_) -
    reinterpret_cast<char*>(&e00_)) + sizeof(e22_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix3d)
}

void Matrix3d::SharedCtor() {
  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e22_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e22_));
}

Matrix3d::~Matrix3d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix3d)
  SharedDtor();
}

void Matrix3d::SharedDtor() {
}

void Matrix3d::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Matrix3d::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Matrix3d& Matrix3d::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix3d.base);
  return *internal_default_instance();
}


void Matrix3d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e22_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e22_));
  _internal_metadata_.Clear();
}

bool Matrix3d::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Matrix3d)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double e00 = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e00_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e01 = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e01_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e02 = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e02_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e10 = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e10_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e11 = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(41u /* 41 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e11_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e12 = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e12_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e20 = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(57u /* 57 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e20_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e21 = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(65u /* 65 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e21_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double e22 = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(73u /* 73 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &e22_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Matrix3d)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Matrix3d)
  return false;
#undef DO_
}

void Matrix3d::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Matrix3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double e00 = 1;
  if (this->e00() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->e00(), output);
  }

  // double e01 = 2;
  if (this->e01() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->e01(), output);
  }

  // double e02 = 3;
  if (this->e02() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->e02(), output);
  }

  // double e10 = 4;
  if (this->e10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->e10(), output);
  }

  // double e11 = 5;
  if (this->e11() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->e11(), output);
  }

  // double e12 = 6;
  if (this->e12() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->e12(), output);
  }

  // double e20 = 7;
  if (this->e20() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->e20(), output);
  }

  // double e21 = 8;
  if (this->e21() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->e21(), output);
  }

  // double e22 = 9;
  if (this->e22() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(9, this->e22(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Matrix3d)
}

::google::protobuf::uint8* Matrix3d::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double e00 = 1;
  if (this->e00() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->e00(), target);
  }

  // double e01 = 2;
  if (this->e01() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->e01(), target);
  }

  // double e02 = 3;
  if (this->e02() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->e02(), target);
  }

  // double e10 = 4;
  if (this->e10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->e10(), target);
  }

  // double e11 = 5;
  if (this->e11() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->e11(), target);
  }

  // double e12 = 6;
  if (this->e12() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->e12(), target);
  }

  // double e20 = 7;
  if (this->e20() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->e20(), target);
  }

  // double e21 = 8;
  if (this->e21() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->e21(), target);
  }

  // double e22 = 9;
  if (this->e22() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(9, this->e22(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix3d)
  return target;
}

size_t Matrix3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix3d)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double e00 = 1;
  if (this->e00() != 0) {
    total_size += 1 + 8;
  }

  // double e01 = 2;
  if (this->e01() != 0) {
    total_size += 1 + 8;
  }

  // double e02 = 3;
  if (this->e02() != 0) {
    total_size += 1 + 8;
  }

  // double e10 = 4;
  if (this->e10() != 0) {
    total_size += 1 + 8;
  }

  // double e11 = 5;
  if (this->e11() != 0) {
    total_size += 1 + 8;
  }

  // double e12 = 6;
  if (this->e12() != 0) {
    total_size += 1 + 8;
  }

  // double e20 = 7;
  if (this->e20() != 0) {
    total_size += 1 + 8;
  }

  // double e21 = 8;
  if (this->e21() != 0) {
    total_size += 1 + 8;
  }

  // double e22 = 9;
  if (this->e22() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Matrix3d::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Matrix3d)
  GOOGLE_DCHECK_NE(&from, this);
  const Matrix3d* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Matrix3d>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Matrix3d)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Matrix3d)
    MergeFrom(*source);
  }
}

void Matrix3d::MergeFrom(const Matrix3d& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix3d)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.e00() != 0) {
    set_e00(from.e00());
  }
  if (from.e01() != 0) {
    set_e01(from.e01());
  }
  if (from.e02() != 0) {
    set_e02(from.e02());
  }
  if (from.e10() != 0) {
    set_e10(from.e10());
  }
  if (from.e11() != 0) {
    set_e11(from.e11());
  }
  if (from.e12() != 0) {
    set_e12(from.e12());
  }
  if (from.e20() != 0) {
    set_e20(from.e20());
  }
  if (from.e21() != 0) {
    set_e21(from.e21());
  }
  if (from.e22() != 0) {
    set_e22(from.e22());
  }
}

void Matrix3d::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Matrix3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Matrix3d::CopyFrom(const Matrix3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix3d::IsInitialized() const {
  return true;
}

void Matrix3d::Swap(Matrix3d* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Matrix3d::InternalSwap(Matrix3d* other) {
  using std::swap;
  swap(e00_, other->e00_);
  swap(e01_, other->e01_);
  swap(e02_, other->e02_);
  swap(e10_, other->e10_);
  swap(e11_, other->e11_);
  swap(e12_, other->e12_);
  swap(e20_, other->e20_);
  swap(e21_, other->e21_);
  swap(e22_, other->e22_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Matrix3d::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Matrix3f::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Matrix3f::kE00FieldNumber;
const int Matrix3f::kE01FieldNumber;
const int Matrix3f::kE02FieldNumber;
const int Matrix3f::kE10FieldNumber;
const int Matrix3f::kE11FieldNumber;
const int Matrix3f::kE12FieldNumber;
const int Matrix3f::kE20FieldNumber;
const int Matrix3f::kE21FieldNumber;
const int Matrix3f::kE22FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Matrix3f::Matrix3f()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix3f.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Matrix3f)
}
Matrix3f::Matrix3f(const Matrix3f& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&e00_, &from.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&e22_) -
    reinterpret_cast<char*>(&e00_)) + sizeof(e22_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix3f)
}

void Matrix3f::SharedCtor() {
  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e22_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e22_));
}

Matrix3f::~Matrix3f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix3f)
  SharedDtor();
}

void Matrix3f::SharedDtor() {
}

void Matrix3f::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Matrix3f::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Matrix3f& Matrix3f::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Matrix3f.base);
  return *internal_default_instance();
}


void Matrix3f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&e22_) -
      reinterpret_cast<char*>(&e00_)) + sizeof(e22_));
  _internal_metadata_.Clear();
}

bool Matrix3f::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Matrix3f)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float e00 = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e00_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e01 = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e01_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e02 = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e02_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e10 = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e10_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e11 = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e11_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e12 = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(53u /* 53 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e12_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e20 = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(61u /* 61 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e20_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e21 = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(69u /* 69 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e21_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float e22 = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(77u /* 77 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &e22_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Matrix3f)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Matrix3f)
  return false;
#undef DO_
}

void Matrix3f::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Matrix3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float e00 = 1;
  if (this->e00() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->e00(), output);
  }

  // float e01 = 2;
  if (this->e01() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->e01(), output);
  }

  // float e02 = 3;
  if (this->e02() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->e02(), output);
  }

  // float e10 = 4;
  if (this->e10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->e10(), output);
  }

  // float e11 = 5;
  if (this->e11() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->e11(), output);
  }

  // float e12 = 6;
  if (this->e12() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(6, this->e12(), output);
  }

  // float e20 = 7;
  if (this->e20() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(7, this->e20(), output);
  }

  // float e21 = 8;
  if (this->e21() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(8, this->e21(), output);
  }

  // float e22 = 9;
  if (this->e22() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(9, this->e22(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Matrix3f)
}

::google::protobuf::uint8* Matrix3f::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float e00 = 1;
  if (this->e00() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->e00(), target);
  }

  // float e01 = 2;
  if (this->e01() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->e01(), target);
  }

  // float e02 = 3;
  if (this->e02() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->e02(), target);
  }

  // float e10 = 4;
  if (this->e10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->e10(), target);
  }

  // float e11 = 5;
  if (this->e11() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->e11(), target);
  }

  // float e12 = 6;
  if (this->e12() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(6, this->e12(), target);
  }

  // float e20 = 7;
  if (this->e20() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(7, this->e20(), target);
  }

  // float e21 = 8;
  if (this->e21() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(8, this->e21(), target);
  }

  // float e22 = 9;
  if (this->e22() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(9, this->e22(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix3f)
  return target;
}

size_t Matrix3f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix3f)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float e00 = 1;
  if (this->e00() != 0) {
    total_size += 1 + 4;
  }

  // float e01 = 2;
  if (this->e01() != 0) {
    total_size += 1 + 4;
  }

  // float e02 = 3;
  if (this->e02() != 0) {
    total_size += 1 + 4;
  }

  // float e10 = 4;
  if (this->e10() != 0) {
    total_size += 1 + 4;
  }

  // float e11 = 5;
  if (this->e11() != 0) {
    total_size += 1 + 4;
  }

  // float e12 = 6;
  if (this->e12() != 0) {
    total_size += 1 + 4;
  }

  // float e20 = 7;
  if (this->e20() != 0) {
    total_size += 1 + 4;
  }

  // float e21 = 8;
  if (this->e21() != 0) {
    total_size += 1 + 4;
  }

  // float e22 = 9;
  if (this->e22() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Matrix3f::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Matrix3f)
  GOOGLE_DCHECK_NE(&from, this);
  const Matrix3f* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Matrix3f>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Matrix3f)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Matrix3f)
    MergeFrom(*source);
  }
}

void Matrix3f::MergeFrom(const Matrix3f& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix3f)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.e00() != 0) {
    set_e00(from.e00());
  }
  if (from.e01() != 0) {
    set_e01(from.e01());
  }
  if (from.e02() != 0) {
    set_e02(from.e02());
  }
  if (from.e10() != 0) {
    set_e10(from.e10());
  }
  if (from.e11() != 0) {
    set_e11(from.e11());
  }
  if (from.e12() != 0) {
    set_e12(from.e12());
  }
  if (from.e20() != 0) {
    set_e20(from.e20());
  }
  if (from.e21() != 0) {
    set_e21(from.e21());
  }
  if (from.e22() != 0) {
    set_e22(from.e22());
  }
}

void Matrix3f::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Matrix3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Matrix3f::CopyFrom(const Matrix3f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix3f::IsInitialized() const {
  return true;
}

void Matrix3f::Swap(Matrix3f* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Matrix3f::InternalSwap(Matrix3f* other) {
  using std::swap;
  swap(e00_, other->e00_);
  swap(e01_, other->e01_);
  swap(e02_, other->e02_);
  swap(e10_, other->e10_);
  swap(e11_, other->e11_);
  swap(e12_, other->e12_);
  swap(e20_, other->e20_);
  swap(e21_, other->e21_);
  swap(e22_, other->e22_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Matrix3f::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Quaterniond::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Quaterniond::kWFieldNumber;
const int Quaterniond::kXFieldNumber;
const int Quaterniond::kYFieldNumber;
const int Quaterniond::kZFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Quaterniond::Quaterniond()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Quaterniond.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Quaterniond)
}
Quaterniond::Quaterniond(const Quaterniond& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&w_, &from.w_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&w_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Quaterniond)
}

void Quaterniond::SharedCtor() {
  ::memset(&w_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&w_)) + sizeof(z_));
}

Quaterniond::~Quaterniond() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Quaterniond)
  SharedDtor();
}

void Quaterniond::SharedDtor() {
}

void Quaterniond::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Quaterniond::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Quaterniond& Quaterniond::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Quaterniond.base);
  return *internal_default_instance();
}


void Quaterniond::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Quaterniond)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&w_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&w_)) + sizeof(z_));
  _internal_metadata_.Clear();
}

bool Quaterniond::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Quaterniond)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double w = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &w_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double x = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double y = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double z = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &z_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Quaterniond)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Quaterniond)
  return false;
#undef DO_
}

void Quaterniond::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Quaterniond)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double w = 1;
  if (this->w() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->w(), output);
  }

  // double x = 2;
  if (this->x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->x(), output);
  }

  // double y = 3;
  if (this->y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->y(), output);
  }

  // double z = 4;
  if (this->z() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->z(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Quaterniond)
}

::google::protobuf::uint8* Quaterniond::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Quaterniond)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double w = 1;
  if (this->w() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->w(), target);
  }

  // double x = 2;
  if (this->x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->x(), target);
  }

  // double y = 3;
  if (this->y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->y(), target);
  }

  // double z = 4;
  if (this->z() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->z(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Quaterniond)
  return target;
}

size_t Quaterniond::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Quaterniond)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double w = 1;
  if (this->w() != 0) {
    total_size += 1 + 8;
  }

  // double x = 2;
  if (this->x() != 0) {
    total_size += 1 + 8;
  }

  // double y = 3;
  if (this->y() != 0) {
    total_size += 1 + 8;
  }

  // double z = 4;
  if (this->z() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Quaterniond::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Quaterniond)
  GOOGLE_DCHECK_NE(&from, this);
  const Quaterniond* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Quaterniond>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Quaterniond)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Quaterniond)
    MergeFrom(*source);
  }
}

void Quaterniond::MergeFrom(const Quaterniond& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Quaterniond)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.w() != 0) {
    set_w(from.w());
  }
  if (from.x() != 0) {
    set_x(from.x());
  }
  if (from.y() != 0) {
    set_y(from.y());
  }
  if (from.z() != 0) {
    set_z(from.z());
  }
}

void Quaterniond::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Quaterniond)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Quaterniond::CopyFrom(const Quaterniond& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Quaterniond)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaterniond::IsInitialized() const {
  return true;
}

void Quaterniond::Swap(Quaterniond* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Quaterniond::InternalSwap(Quaterniond* other) {
  using std::swap;
  swap(w_, other->w_);
  swap(x_, other->x_);
  swap(y_, other->y_);
  swap(z_, other->z_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Quaterniond::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Quaternionf::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Quaternionf::kWFieldNumber;
const int Quaternionf::kXFieldNumber;
const int Quaternionf::kYFieldNumber;
const int Quaternionf::kZFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Quaternionf::Quaternionf()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Quaternionf.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Quaternionf)
}
Quaternionf::Quaternionf(const Quaternionf& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&w_, &from.w_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&w_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Quaternionf)
}

void Quaternionf::SharedCtor() {
  ::memset(&w_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&w_)) + sizeof(z_));
}

Quaternionf::~Quaternionf() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Quaternionf)
  SharedDtor();
}

void Quaternionf::SharedDtor() {
}

void Quaternionf::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Quaternionf::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Quaternionf& Quaternionf::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Quaternionf.base);
  return *internal_default_instance();
}


void Quaternionf::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Quaternionf)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&w_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&w_)) + sizeof(z_));
  _internal_metadata_.Clear();
}

bool Quaternionf::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Quaternionf)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float w = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &w_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float x = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float y = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float z = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &z_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Quaternionf)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Quaternionf)
  return false;
#undef DO_
}

void Quaternionf::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Quaternionf)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float w = 1;
  if (this->w() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->w(), output);
  }

  // float x = 2;
  if (this->x() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->x(), output);
  }

  // float y = 3;
  if (this->y() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->y(), output);
  }

  // float z = 4;
  if (this->z() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->z(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Quaternionf)
}

::google::protobuf::uint8* Quaternionf::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Quaternionf)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float w = 1;
  if (this->w() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->w(), target);
  }

  // float x = 2;
  if (this->x() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->x(), target);
  }

  // float y = 3;
  if (this->y() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->y(), target);
  }

  // float z = 4;
  if (this->z() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->z(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Quaternionf)
  return target;
}

size_t Quaternionf::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Quaternionf)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float w = 1;
  if (this->w() != 0) {
    total_size += 1 + 4;
  }

  // float x = 2;
  if (this->x() != 0) {
    total_size += 1 + 4;
  }

  // float y = 3;
  if (this->y() != 0) {
    total_size += 1 + 4;
  }

  // float z = 4;
  if (this->z() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Quaternionf::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Quaternionf)
  GOOGLE_DCHECK_NE(&from, this);
  const Quaternionf* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Quaternionf>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Quaternionf)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Quaternionf)
    MergeFrom(*source);
  }
}

void Quaternionf::MergeFrom(const Quaternionf& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Quaternionf)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.w() != 0) {
    set_w(from.w());
  }
  if (from.x() != 0) {
    set_x(from.x());
  }
  if (from.y() != 0) {
    set_y(from.y());
  }
  if (from.z() != 0) {
    set_z(from.z());
  }
}

void Quaternionf::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Quaternionf)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Quaternionf::CopyFrom(const Quaternionf& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Quaternionf)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaternionf::IsInitialized() const {
  return true;
}

void Quaternionf::Swap(Quaternionf* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Quaternionf::InternalSwap(Quaternionf* other) {
  using std::swap;
  swap(w_, other->w_);
  swap(x_, other->x_);
  swap(y_, other->y_);
  swap(z_, other->z_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Quaternionf::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Transformation3d::InitAsDefaultInstance() {
  ::esurfing::proto::math::_Transformation3d_default_instance_._instance.get_mutable()->rotation_ = const_cast< ::esurfing::proto::math::Quaterniond*>(
      ::esurfing::proto::math::Quaterniond::internal_default_instance());
  ::esurfing::proto::math::_Transformation3d_default_instance_._instance.get_mutable()->translation_ = const_cast< ::esurfing::proto::math::Vector3d*>(
      ::esurfing::proto::math::Vector3d::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Transformation3d::kRotationFieldNumber;
const int Transformation3d::kTranslationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Transformation3d::Transformation3d()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Transformation3d.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Transformation3d)
}
Transformation3d::Transformation3d(const Transformation3d& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_rotation()) {
    rotation_ = new ::esurfing::proto::math::Quaterniond(*from.rotation_);
  } else {
    rotation_ = NULL;
  }
  if (from.has_translation()) {
    translation_ = new ::esurfing::proto::math::Vector3d(*from.translation_);
  } else {
    translation_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Transformation3d)
}

void Transformation3d::SharedCtor() {
  ::memset(&rotation_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&translation_) -
      reinterpret_cast<char*>(&rotation_)) + sizeof(translation_));
}

Transformation3d::~Transformation3d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Transformation3d)
  SharedDtor();
}

void Transformation3d::SharedDtor() {
  if (this != internal_default_instance()) delete rotation_;
  if (this != internal_default_instance()) delete translation_;
}

void Transformation3d::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Transformation3d::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Transformation3d& Transformation3d::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Transformation3d.base);
  return *internal_default_instance();
}


void Transformation3d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Transformation3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && rotation_ != NULL) {
    delete rotation_;
  }
  rotation_ = NULL;
  if (GetArenaNoVirtual() == NULL && translation_ != NULL) {
    delete translation_;
  }
  translation_ = NULL;
  _internal_metadata_.Clear();
}

bool Transformation3d::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Transformation3d)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .esurfing.proto.math.Quaterniond rotation = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_rotation()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .esurfing.proto.math.Vector3d translation = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_translation()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Transformation3d)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Transformation3d)
  return false;
#undef DO_
}

void Transformation3d::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Transformation3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaterniond rotation = 1;
  if (this->has_rotation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_rotation(), output);
  }

  // .esurfing.proto.math.Vector3d translation = 2;
  if (this->has_translation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_translation(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Transformation3d)
}

::google::protobuf::uint8* Transformation3d::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Transformation3d)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaterniond rotation = 1;
  if (this->has_rotation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_rotation(), deterministic, target);
  }

  // .esurfing.proto.math.Vector3d translation = 2;
  if (this->has_translation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_translation(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Transformation3d)
  return target;
}

size_t Transformation3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Transformation3d)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .esurfing.proto.math.Quaterniond rotation = 1;
  if (this->has_rotation()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *rotation_);
  }

  // .esurfing.proto.math.Vector3d translation = 2;
  if (this->has_translation()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *translation_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Transformation3d::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Transformation3d)
  GOOGLE_DCHECK_NE(&from, this);
  const Transformation3d* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Transformation3d>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Transformation3d)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Transformation3d)
    MergeFrom(*source);
  }
}

void Transformation3d::MergeFrom(const Transformation3d& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Transformation3d)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_rotation()) {
    mutable_rotation()->::esurfing::proto::math::Quaterniond::MergeFrom(from.rotation());
  }
  if (from.has_translation()) {
    mutable_translation()->::esurfing::proto::math::Vector3d::MergeFrom(from.translation());
  }
}

void Transformation3d::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Transformation3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Transformation3d::CopyFrom(const Transformation3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Transformation3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Transformation3d::IsInitialized() const {
  return true;
}

void Transformation3d::Swap(Transformation3d* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Transformation3d::InternalSwap(Transformation3d* other) {
  using std::swap;
  swap(rotation_, other->rotation_);
  swap(translation_, other->translation_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Transformation3d::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Transformation3f::InitAsDefaultInstance() {
  ::esurfing::proto::math::_Transformation3f_default_instance_._instance.get_mutable()->rotation_ = const_cast< ::esurfing::proto::math::Quaternionf*>(
      ::esurfing::proto::math::Quaternionf::internal_default_instance());
  ::esurfing::proto::math::_Transformation3f_default_instance_._instance.get_mutable()->translation_ = const_cast< ::esurfing::proto::math::Vector3f*>(
      ::esurfing::proto::math::Vector3f::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Transformation3f::kRotationFieldNumber;
const int Transformation3f::kTranslationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Transformation3f::Transformation3f()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Transformation3f.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:esurfing.proto.math.Transformation3f)
}
Transformation3f::Transformation3f(const Transformation3f& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_rotation()) {
    rotation_ = new ::esurfing::proto::math::Quaternionf(*from.rotation_);
  } else {
    rotation_ = NULL;
  }
  if (from.has_translation()) {
    translation_ = new ::esurfing::proto::math::Vector3f(*from.translation_);
  } else {
    translation_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Transformation3f)
}

void Transformation3f::SharedCtor() {
  ::memset(&rotation_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&translation_) -
      reinterpret_cast<char*>(&rotation_)) + sizeof(translation_));
}

Transformation3f::~Transformation3f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Transformation3f)
  SharedDtor();
}

void Transformation3f::SharedDtor() {
  if (this != internal_default_instance()) delete rotation_;
  if (this != internal_default_instance()) delete translation_;
}

void Transformation3f::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Transformation3f::descriptor() {
  ::protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Transformation3f& Transformation3f::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_proto_2fmath_2fgeo_2eproto::scc_info_Transformation3f.base);
  return *internal_default_instance();
}


void Transformation3f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Transformation3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && rotation_ != NULL) {
    delete rotation_;
  }
  rotation_ = NULL;
  if (GetArenaNoVirtual() == NULL && translation_ != NULL) {
    delete translation_;
  }
  translation_ = NULL;
  _internal_metadata_.Clear();
}

bool Transformation3f::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:esurfing.proto.math.Transformation3f)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .esurfing.proto.math.Quaternionf rotation = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_rotation()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .esurfing.proto.math.Vector3f translation = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_translation()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:esurfing.proto.math.Transformation3f)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:esurfing.proto.math.Transformation3f)
  return false;
#undef DO_
}

void Transformation3f::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:esurfing.proto.math.Transformation3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaternionf rotation = 1;
  if (this->has_rotation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_rotation(), output);
  }

  // .esurfing.proto.math.Vector3f translation = 2;
  if (this->has_translation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_translation(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:esurfing.proto.math.Transformation3f)
}

::google::protobuf::uint8* Transformation3f::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Transformation3f)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaternionf rotation = 1;
  if (this->has_rotation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_rotation(), deterministic, target);
  }

  // .esurfing.proto.math.Vector3f translation = 2;
  if (this->has_translation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_translation(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Transformation3f)
  return target;
}

size_t Transformation3f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Transformation3f)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .esurfing.proto.math.Quaternionf rotation = 1;
  if (this->has_rotation()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *rotation_);
  }

  // .esurfing.proto.math.Vector3f translation = 2;
  if (this->has_translation()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *translation_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Transformation3f::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:esurfing.proto.math.Transformation3f)
  GOOGLE_DCHECK_NE(&from, this);
  const Transformation3f* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Transformation3f>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:esurfing.proto.math.Transformation3f)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:esurfing.proto.math.Transformation3f)
    MergeFrom(*source);
  }
}

void Transformation3f::MergeFrom(const Transformation3f& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Transformation3f)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_rotation()) {
    mutable_rotation()->::esurfing::proto::math::Quaternionf::MergeFrom(from.rotation());
  }
  if (from.has_translation()) {
    mutable_translation()->::esurfing::proto::math::Vector3f::MergeFrom(from.translation());
  }
}

void Transformation3f::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:esurfing.proto.math.Transformation3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Transformation3f::CopyFrom(const Transformation3f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Transformation3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Transformation3f::IsInitialized() const {
  return true;
}

void Transformation3f::Swap(Transformation3f* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Transformation3f::InternalSwap(Transformation3f* other) {
  using std::swap;
  swap(rotation_, other->rotation_);
  swap(translation_, other->translation_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Transformation3f::GetMetadata() const {
  protobuf_proto_2fmath_2fgeo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_proto_2fmath_2fgeo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace math
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Vector3i* Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector3i >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Vector3i >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Polyline* Arena::CreateMaybeMessage< ::esurfing::proto::math::Polyline >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Polyline >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Polygon* Arena::CreateMaybeMessage< ::esurfing::proto::math::Polygon >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Polygon >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Points* Arena::CreateMaybeMessage< ::esurfing::proto::math::Points >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Points >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Vector2d* Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector2d >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Vector2d >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Vector2f* Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector2f >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Vector2f >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Vector3d* Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector3d >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Vector3d >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Vector3f* Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector3f >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Vector3f >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Matrix2d* Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix2d >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Matrix2d >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Matrix2f* Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix2f >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Matrix2f >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Matrix3d* Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix3d >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Matrix3d >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Matrix3f* Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix3f >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Matrix3f >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Quaterniond* Arena::CreateMaybeMessage< ::esurfing::proto::math::Quaterniond >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Quaterniond >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Quaternionf* Arena::CreateMaybeMessage< ::esurfing::proto::math::Quaternionf >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Quaternionf >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Transformation3d* Arena::CreateMaybeMessage< ::esurfing::proto::math::Transformation3d >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Transformation3d >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::esurfing::proto::math::Transformation3f* Arena::CreateMaybeMessage< ::esurfing::proto::math::Transformation3f >(Arena* arena) {
  return Arena::CreateInternal< ::esurfing::proto::math::Transformation3f >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)
