// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/math/geo.proto

#ifndef PROTOBUF_INCLUDED_proto_2fmath_2fgeo_2eproto
#define PROTOBUF_INCLUDED_proto_2fmath_2fgeo_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_proto_2fmath_2fgeo_2eproto 

namespace protobuf_proto_2fmath_2fgeo_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[16];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_proto_2fmath_2fgeo_2eproto
namespace esurfing {
namespace proto {
namespace math {
class Matrix2d;
class Matrix2dDefaultTypeInternal;
extern Matrix2dDefaultTypeInternal _Matrix2d_default_instance_;
class Matrix2f;
class Matrix2fDefaultTypeInternal;
extern Matrix2fDefaultTypeInternal _Matrix2f_default_instance_;
class Matrix3d;
class Matrix3dDefaultTypeInternal;
extern Matrix3dDefaultTypeInternal _Matrix3d_default_instance_;
class Matrix3f;
class Matrix3fDefaultTypeInternal;
extern Matrix3fDefaultTypeInternal _Matrix3f_default_instance_;
class Points;
class PointsDefaultTypeInternal;
extern PointsDefaultTypeInternal _Points_default_instance_;
class Polygon;
class PolygonDefaultTypeInternal;
extern PolygonDefaultTypeInternal _Polygon_default_instance_;
class Polyline;
class PolylineDefaultTypeInternal;
extern PolylineDefaultTypeInternal _Polyline_default_instance_;
class Quaterniond;
class QuaterniondDefaultTypeInternal;
extern QuaterniondDefaultTypeInternal _Quaterniond_default_instance_;
class Quaternionf;
class QuaternionfDefaultTypeInternal;
extern QuaternionfDefaultTypeInternal _Quaternionf_default_instance_;
class Transformation3d;
class Transformation3dDefaultTypeInternal;
extern Transformation3dDefaultTypeInternal _Transformation3d_default_instance_;
class Transformation3f;
class Transformation3fDefaultTypeInternal;
extern Transformation3fDefaultTypeInternal _Transformation3f_default_instance_;
class Vector2d;
class Vector2dDefaultTypeInternal;
extern Vector2dDefaultTypeInternal _Vector2d_default_instance_;
class Vector2f;
class Vector2fDefaultTypeInternal;
extern Vector2fDefaultTypeInternal _Vector2f_default_instance_;
class Vector3d;
class Vector3dDefaultTypeInternal;
extern Vector3dDefaultTypeInternal _Vector3d_default_instance_;
class Vector3f;
class Vector3fDefaultTypeInternal;
extern Vector3fDefaultTypeInternal _Vector3f_default_instance_;
class Vector3i;
class Vector3iDefaultTypeInternal;
extern Vector3iDefaultTypeInternal _Vector3i_default_instance_;
}  // namespace math
}  // namespace proto
}  // namespace esurfing
namespace google {
namespace protobuf {
template<> ::esurfing::proto::math::Matrix2d* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix2d>(Arena*);
template<> ::esurfing::proto::math::Matrix2f* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix2f>(Arena*);
template<> ::esurfing::proto::math::Matrix3d* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix3d>(Arena*);
template<> ::esurfing::proto::math::Matrix3f* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix3f>(Arena*);
template<> ::esurfing::proto::math::Points* Arena::CreateMaybeMessage<::esurfing::proto::math::Points>(Arena*);
template<> ::esurfing::proto::math::Polygon* Arena::CreateMaybeMessage<::esurfing::proto::math::Polygon>(Arena*);
template<> ::esurfing::proto::math::Polyline* Arena::CreateMaybeMessage<::esurfing::proto::math::Polyline>(Arena*);
template<> ::esurfing::proto::math::Quaterniond* Arena::CreateMaybeMessage<::esurfing::proto::math::Quaterniond>(Arena*);
template<> ::esurfing::proto::math::Quaternionf* Arena::CreateMaybeMessage<::esurfing::proto::math::Quaternionf>(Arena*);
template<> ::esurfing::proto::math::Transformation3d* Arena::CreateMaybeMessage<::esurfing::proto::math::Transformation3d>(Arena*);
template<> ::esurfing::proto::math::Transformation3f* Arena::CreateMaybeMessage<::esurfing::proto::math::Transformation3f>(Arena*);
template<> ::esurfing::proto::math::Vector2d* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector2d>(Arena*);
template<> ::esurfing::proto::math::Vector2f* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector2f>(Arena*);
template<> ::esurfing::proto::math::Vector3d* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector3d>(Arena*);
template<> ::esurfing::proto::math::Vector3f* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector3f>(Arena*);
template<> ::esurfing::proto::math::Vector3i* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector3i>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace esurfing {
namespace proto {
namespace math {

// ===================================================================

class Vector3i : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector3i) */ {
 public:
  Vector3i();
  virtual ~Vector3i();

  Vector3i(const Vector3i& from);

  inline Vector3i& operator=(const Vector3i& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Vector3i(Vector3i&& from) noexcept
    : Vector3i() {
    *this = ::std::move(from);
  }

  inline Vector3i& operator=(Vector3i&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Vector3i& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Vector3i* internal_default_instance() {
    return reinterpret_cast<const Vector3i*>(
               &_Vector3i_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(Vector3i* other);
  friend void swap(Vector3i& a, Vector3i& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Vector3i* New() const final {
    return CreateMaybeMessage<Vector3i>(NULL);
  }

  Vector3i* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Vector3i>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Vector3i& from);
  void MergeFrom(const Vector3i& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3i* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 x = 1;
  void clear_x();
  static const int kXFieldNumber = 1;
  ::google::protobuf::int64 x() const;
  void set_x(::google::protobuf::int64 value);

  // int64 y = 2;
  void clear_y();
  static const int kYFieldNumber = 2;
  ::google::protobuf::int64 y() const;
  void set_y(::google::protobuf::int64 value);

  // int64 z = 3;
  void clear_z();
  static const int kZFieldNumber = 3;
  ::google::protobuf::int64 z() const;
  void set_z(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector3i)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 x_;
  ::google::protobuf::int64 y_;
  ::google::protobuf::int64 z_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Polyline : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Polyline) */ {
 public:
  Polyline();
  virtual ~Polyline();

  Polyline(const Polyline& from);

  inline Polyline& operator=(const Polyline& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Polyline(Polyline&& from) noexcept
    : Polyline() {
    *this = ::std::move(from);
  }

  inline Polyline& operator=(Polyline&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Polyline& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Polyline* internal_default_instance() {
    return reinterpret_cast<const Polyline*>(
               &_Polyline_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(Polyline* other);
  friend void swap(Polyline& a, Polyline& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Polyline* New() const final {
    return CreateMaybeMessage<Polyline>(NULL);
  }

  Polyline* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Polyline>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Polyline& from);
  void MergeFrom(const Polyline& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polyline* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.math.Vector3d points = 1;
  int points_size() const;
  void clear_points();
  static const int kPointsFieldNumber = 1;
  ::esurfing::proto::math::Vector3d* mutable_points(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
      mutable_points();
  const ::esurfing::proto::math::Vector3d& points(int index) const;
  ::esurfing::proto::math::Vector3d* add_points();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
      points() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Polyline)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d > points_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Polygon : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Polygon) */ {
 public:
  Polygon();
  virtual ~Polygon();

  Polygon(const Polygon& from);

  inline Polygon& operator=(const Polygon& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Polygon(Polygon&& from) noexcept
    : Polygon() {
    *this = ::std::move(from);
  }

  inline Polygon& operator=(Polygon&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Polygon& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Polygon* internal_default_instance() {
    return reinterpret_cast<const Polygon*>(
               &_Polygon_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(Polygon* other);
  friend void swap(Polygon& a, Polygon& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Polygon* New() const final {
    return CreateMaybeMessage<Polygon>(NULL);
  }

  Polygon* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Polygon>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Polygon& from);
  void MergeFrom(const Polygon& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polygon* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.math.Vector2f points = 1;
  int points_size() const;
  void clear_points();
  static const int kPointsFieldNumber = 1;
  ::esurfing::proto::math::Vector2f* mutable_points(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector2f >*
      mutable_points();
  const ::esurfing::proto::math::Vector2f& points(int index) const;
  ::esurfing::proto::math::Vector2f* add_points();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector2f >&
      points() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Polygon)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector2f > points_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Points : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Points) */ {
 public:
  Points();
  virtual ~Points();

  Points(const Points& from);

  inline Points& operator=(const Points& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Points(Points&& from) noexcept
    : Points() {
    *this = ::std::move(from);
  }

  inline Points& operator=(Points&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Points& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Points* internal_default_instance() {
    return reinterpret_cast<const Points*>(
               &_Points_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(Points* other);
  friend void swap(Points& a, Points& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Points* New() const final {
    return CreateMaybeMessage<Points>(NULL);
  }

  Points* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Points>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Points& from);
  void MergeFrom(const Points& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Points* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .esurfing.proto.math.Vector3d points = 1;
  int points_size() const;
  void clear_points();
  static const int kPointsFieldNumber = 1;
  ::esurfing::proto::math::Vector3d* mutable_points(int index);
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
      mutable_points();
  const ::esurfing::proto::math::Vector3d& points(int index) const;
  ::esurfing::proto::math::Vector3d* add_points();
  const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
      points() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Points)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d > points_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Vector2d : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector2d) */ {
 public:
  Vector2d();
  virtual ~Vector2d();

  Vector2d(const Vector2d& from);

  inline Vector2d& operator=(const Vector2d& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Vector2d(Vector2d&& from) noexcept
    : Vector2d() {
    *this = ::std::move(from);
  }

  inline Vector2d& operator=(Vector2d&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Vector2d& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Vector2d* internal_default_instance() {
    return reinterpret_cast<const Vector2d*>(
               &_Vector2d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(Vector2d* other);
  friend void swap(Vector2d& a, Vector2d& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Vector2d* New() const final {
    return CreateMaybeMessage<Vector2d>(NULL);
  }

  Vector2d* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Vector2d>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Vector2d& from);
  void MergeFrom(const Vector2d& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector2d* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double x = 1;
  void clear_x();
  static const int kXFieldNumber = 1;
  double x() const;
  void set_x(double value);

  // double y = 2;
  void clear_y();
  static const int kYFieldNumber = 2;
  double y() const;
  void set_y(double value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector2d)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double x_;
  double y_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Vector2f : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector2f) */ {
 public:
  Vector2f();
  virtual ~Vector2f();

  Vector2f(const Vector2f& from);

  inline Vector2f& operator=(const Vector2f& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Vector2f(Vector2f&& from) noexcept
    : Vector2f() {
    *this = ::std::move(from);
  }

  inline Vector2f& operator=(Vector2f&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Vector2f& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Vector2f* internal_default_instance() {
    return reinterpret_cast<const Vector2f*>(
               &_Vector2f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(Vector2f* other);
  friend void swap(Vector2f& a, Vector2f& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Vector2f* New() const final {
    return CreateMaybeMessage<Vector2f>(NULL);
  }

  Vector2f* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Vector2f>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Vector2f& from);
  void MergeFrom(const Vector2f& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector2f* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float x = 1;
  void clear_x();
  static const int kXFieldNumber = 1;
  float x() const;
  void set_x(float value);

  // float y = 2;
  void clear_y();
  static const int kYFieldNumber = 2;
  float y() const;
  void set_y(float value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector2f)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float x_;
  float y_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Vector3d : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector3d) */ {
 public:
  Vector3d();
  virtual ~Vector3d();

  Vector3d(const Vector3d& from);

  inline Vector3d& operator=(const Vector3d& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Vector3d(Vector3d&& from) noexcept
    : Vector3d() {
    *this = ::std::move(from);
  }

  inline Vector3d& operator=(Vector3d&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Vector3d& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Vector3d* internal_default_instance() {
    return reinterpret_cast<const Vector3d*>(
               &_Vector3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(Vector3d* other);
  friend void swap(Vector3d& a, Vector3d& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Vector3d* New() const final {
    return CreateMaybeMessage<Vector3d>(NULL);
  }

  Vector3d* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Vector3d>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Vector3d& from);
  void MergeFrom(const Vector3d& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3d* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double x = 1;
  void clear_x();
  static const int kXFieldNumber = 1;
  double x() const;
  void set_x(double value);

  // double y = 2;
  void clear_y();
  static const int kYFieldNumber = 2;
  double y() const;
  void set_y(double value);

  // double z = 3;
  void clear_z();
  static const int kZFieldNumber = 3;
  double z() const;
  void set_z(double value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector3d)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double x_;
  double y_;
  double z_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Vector3f : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector3f) */ {
 public:
  Vector3f();
  virtual ~Vector3f();

  Vector3f(const Vector3f& from);

  inline Vector3f& operator=(const Vector3f& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Vector3f(Vector3f&& from) noexcept
    : Vector3f() {
    *this = ::std::move(from);
  }

  inline Vector3f& operator=(Vector3f&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Vector3f& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Vector3f* internal_default_instance() {
    return reinterpret_cast<const Vector3f*>(
               &_Vector3f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void Swap(Vector3f* other);
  friend void swap(Vector3f& a, Vector3f& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Vector3f* New() const final {
    return CreateMaybeMessage<Vector3f>(NULL);
  }

  Vector3f* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Vector3f>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Vector3f& from);
  void MergeFrom(const Vector3f& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3f* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float x = 1;
  void clear_x();
  static const int kXFieldNumber = 1;
  float x() const;
  void set_x(float value);

  // float y = 2;
  void clear_y();
  static const int kYFieldNumber = 2;
  float y() const;
  void set_y(float value);

  // float z = 3;
  void clear_z();
  static const int kZFieldNumber = 3;
  float z() const;
  void set_z(float value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector3f)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float x_;
  float y_;
  float z_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Matrix2d : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix2d) */ {
 public:
  Matrix2d();
  virtual ~Matrix2d();

  Matrix2d(const Matrix2d& from);

  inline Matrix2d& operator=(const Matrix2d& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Matrix2d(Matrix2d&& from) noexcept
    : Matrix2d() {
    *this = ::std::move(from);
  }

  inline Matrix2d& operator=(Matrix2d&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Matrix2d& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Matrix2d* internal_default_instance() {
    return reinterpret_cast<const Matrix2d*>(
               &_Matrix2d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void Swap(Matrix2d* other);
  friend void swap(Matrix2d& a, Matrix2d& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Matrix2d* New() const final {
    return CreateMaybeMessage<Matrix2d>(NULL);
  }

  Matrix2d* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Matrix2d>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Matrix2d& from);
  void MergeFrom(const Matrix2d& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix2d* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double e00 = 1;
  void clear_e00();
  static const int kE00FieldNumber = 1;
  double e00() const;
  void set_e00(double value);

  // double e01 = 2;
  void clear_e01();
  static const int kE01FieldNumber = 2;
  double e01() const;
  void set_e01(double value);

  // double e10 = 3;
  void clear_e10();
  static const int kE10FieldNumber = 3;
  double e10() const;
  void set_e10(double value);

  // double e11 = 4;
  void clear_e11();
  static const int kE11FieldNumber = 4;
  double e11() const;
  void set_e11(double value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix2d)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double e00_;
  double e01_;
  double e10_;
  double e11_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Matrix2f : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix2f) */ {
 public:
  Matrix2f();
  virtual ~Matrix2f();

  Matrix2f(const Matrix2f& from);

  inline Matrix2f& operator=(const Matrix2f& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Matrix2f(Matrix2f&& from) noexcept
    : Matrix2f() {
    *this = ::std::move(from);
  }

  inline Matrix2f& operator=(Matrix2f&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Matrix2f& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Matrix2f* internal_default_instance() {
    return reinterpret_cast<const Matrix2f*>(
               &_Matrix2f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void Swap(Matrix2f* other);
  friend void swap(Matrix2f& a, Matrix2f& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Matrix2f* New() const final {
    return CreateMaybeMessage<Matrix2f>(NULL);
  }

  Matrix2f* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Matrix2f>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Matrix2f& from);
  void MergeFrom(const Matrix2f& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix2f* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float e00 = 1;
  void clear_e00();
  static const int kE00FieldNumber = 1;
  float e00() const;
  void set_e00(float value);

  // float e01 = 2;
  void clear_e01();
  static const int kE01FieldNumber = 2;
  float e01() const;
  void set_e01(float value);

  // float e10 = 3;
  void clear_e10();
  static const int kE10FieldNumber = 3;
  float e10() const;
  void set_e10(float value);

  // float e11 = 4;
  void clear_e11();
  static const int kE11FieldNumber = 4;
  float e11() const;
  void set_e11(float value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix2f)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float e00_;
  float e01_;
  float e10_;
  float e11_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Matrix3d : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix3d) */ {
 public:
  Matrix3d();
  virtual ~Matrix3d();

  Matrix3d(const Matrix3d& from);

  inline Matrix3d& operator=(const Matrix3d& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Matrix3d(Matrix3d&& from) noexcept
    : Matrix3d() {
    *this = ::std::move(from);
  }

  inline Matrix3d& operator=(Matrix3d&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Matrix3d& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Matrix3d* internal_default_instance() {
    return reinterpret_cast<const Matrix3d*>(
               &_Matrix3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void Swap(Matrix3d* other);
  friend void swap(Matrix3d& a, Matrix3d& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Matrix3d* New() const final {
    return CreateMaybeMessage<Matrix3d>(NULL);
  }

  Matrix3d* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Matrix3d>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Matrix3d& from);
  void MergeFrom(const Matrix3d& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix3d* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double e00 = 1;
  void clear_e00();
  static const int kE00FieldNumber = 1;
  double e00() const;
  void set_e00(double value);

  // double e01 = 2;
  void clear_e01();
  static const int kE01FieldNumber = 2;
  double e01() const;
  void set_e01(double value);

  // double e02 = 3;
  void clear_e02();
  static const int kE02FieldNumber = 3;
  double e02() const;
  void set_e02(double value);

  // double e10 = 4;
  void clear_e10();
  static const int kE10FieldNumber = 4;
  double e10() const;
  void set_e10(double value);

  // double e11 = 5;
  void clear_e11();
  static const int kE11FieldNumber = 5;
  double e11() const;
  void set_e11(double value);

  // double e12 = 6;
  void clear_e12();
  static const int kE12FieldNumber = 6;
  double e12() const;
  void set_e12(double value);

  // double e20 = 7;
  void clear_e20();
  static const int kE20FieldNumber = 7;
  double e20() const;
  void set_e20(double value);

  // double e21 = 8;
  void clear_e21();
  static const int kE21FieldNumber = 8;
  double e21() const;
  void set_e21(double value);

  // double e22 = 9;
  void clear_e22();
  static const int kE22FieldNumber = 9;
  double e22() const;
  void set_e22(double value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix3d)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double e00_;
  double e01_;
  double e02_;
  double e10_;
  double e11_;
  double e12_;
  double e20_;
  double e21_;
  double e22_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Matrix3f : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix3f) */ {
 public:
  Matrix3f();
  virtual ~Matrix3f();

  Matrix3f(const Matrix3f& from);

  inline Matrix3f& operator=(const Matrix3f& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Matrix3f(Matrix3f&& from) noexcept
    : Matrix3f() {
    *this = ::std::move(from);
  }

  inline Matrix3f& operator=(Matrix3f&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Matrix3f& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Matrix3f* internal_default_instance() {
    return reinterpret_cast<const Matrix3f*>(
               &_Matrix3f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void Swap(Matrix3f* other);
  friend void swap(Matrix3f& a, Matrix3f& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Matrix3f* New() const final {
    return CreateMaybeMessage<Matrix3f>(NULL);
  }

  Matrix3f* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Matrix3f>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Matrix3f& from);
  void MergeFrom(const Matrix3f& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix3f* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float e00 = 1;
  void clear_e00();
  static const int kE00FieldNumber = 1;
  float e00() const;
  void set_e00(float value);

  // float e01 = 2;
  void clear_e01();
  static const int kE01FieldNumber = 2;
  float e01() const;
  void set_e01(float value);

  // float e02 = 3;
  void clear_e02();
  static const int kE02FieldNumber = 3;
  float e02() const;
  void set_e02(float value);

  // float e10 = 4;
  void clear_e10();
  static const int kE10FieldNumber = 4;
  float e10() const;
  void set_e10(float value);

  // float e11 = 5;
  void clear_e11();
  static const int kE11FieldNumber = 5;
  float e11() const;
  void set_e11(float value);

  // float e12 = 6;
  void clear_e12();
  static const int kE12FieldNumber = 6;
  float e12() const;
  void set_e12(float value);

  // float e20 = 7;
  void clear_e20();
  static const int kE20FieldNumber = 7;
  float e20() const;
  void set_e20(float value);

  // float e21 = 8;
  void clear_e21();
  static const int kE21FieldNumber = 8;
  float e21() const;
  void set_e21(float value);

  // float e22 = 9;
  void clear_e22();
  static const int kE22FieldNumber = 9;
  float e22() const;
  void set_e22(float value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix3f)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float e00_;
  float e01_;
  float e02_;
  float e10_;
  float e11_;
  float e12_;
  float e20_;
  float e21_;
  float e22_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Quaterniond : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Quaterniond) */ {
 public:
  Quaterniond();
  virtual ~Quaterniond();

  Quaterniond(const Quaterniond& from);

  inline Quaterniond& operator=(const Quaterniond& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Quaterniond(Quaterniond&& from) noexcept
    : Quaterniond() {
    *this = ::std::move(from);
  }

  inline Quaterniond& operator=(Quaterniond&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Quaterniond& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Quaterniond* internal_default_instance() {
    return reinterpret_cast<const Quaterniond*>(
               &_Quaterniond_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void Swap(Quaterniond* other);
  friend void swap(Quaterniond& a, Quaterniond& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Quaterniond* New() const final {
    return CreateMaybeMessage<Quaterniond>(NULL);
  }

  Quaterniond* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Quaterniond>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Quaterniond& from);
  void MergeFrom(const Quaterniond& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quaterniond* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double w = 1;
  void clear_w();
  static const int kWFieldNumber = 1;
  double w() const;
  void set_w(double value);

  // double x = 2;
  void clear_x();
  static const int kXFieldNumber = 2;
  double x() const;
  void set_x(double value);

  // double y = 3;
  void clear_y();
  static const int kYFieldNumber = 3;
  double y() const;
  void set_y(double value);

  // double z = 4;
  void clear_z();
  static const int kZFieldNumber = 4;
  double z() const;
  void set_z(double value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Quaterniond)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double w_;
  double x_;
  double y_;
  double z_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Quaternionf : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Quaternionf) */ {
 public:
  Quaternionf();
  virtual ~Quaternionf();

  Quaternionf(const Quaternionf& from);

  inline Quaternionf& operator=(const Quaternionf& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Quaternionf(Quaternionf&& from) noexcept
    : Quaternionf() {
    *this = ::std::move(from);
  }

  inline Quaternionf& operator=(Quaternionf&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Quaternionf& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Quaternionf* internal_default_instance() {
    return reinterpret_cast<const Quaternionf*>(
               &_Quaternionf_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void Swap(Quaternionf* other);
  friend void swap(Quaternionf& a, Quaternionf& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Quaternionf* New() const final {
    return CreateMaybeMessage<Quaternionf>(NULL);
  }

  Quaternionf* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Quaternionf>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Quaternionf& from);
  void MergeFrom(const Quaternionf& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quaternionf* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float w = 1;
  void clear_w();
  static const int kWFieldNumber = 1;
  float w() const;
  void set_w(float value);

  // float x = 2;
  void clear_x();
  static const int kXFieldNumber = 2;
  float x() const;
  void set_x(float value);

  // float y = 3;
  void clear_y();
  static const int kYFieldNumber = 3;
  float y() const;
  void set_y(float value);

  // float z = 4;
  void clear_z();
  static const int kZFieldNumber = 4;
  float z() const;
  void set_z(float value);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Quaternionf)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float w_;
  float x_;
  float y_;
  float z_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Transformation3d : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Transformation3d) */ {
 public:
  Transformation3d();
  virtual ~Transformation3d();

  Transformation3d(const Transformation3d& from);

  inline Transformation3d& operator=(const Transformation3d& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Transformation3d(Transformation3d&& from) noexcept
    : Transformation3d() {
    *this = ::std::move(from);
  }

  inline Transformation3d& operator=(Transformation3d&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Transformation3d& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Transformation3d* internal_default_instance() {
    return reinterpret_cast<const Transformation3d*>(
               &_Transformation3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void Swap(Transformation3d* other);
  friend void swap(Transformation3d& a, Transformation3d& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Transformation3d* New() const final {
    return CreateMaybeMessage<Transformation3d>(NULL);
  }

  Transformation3d* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Transformation3d>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Transformation3d& from);
  void MergeFrom(const Transformation3d& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Transformation3d* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .esurfing.proto.math.Quaterniond rotation = 1;
  bool has_rotation() const;
  void clear_rotation();
  static const int kRotationFieldNumber = 1;
  private:
  const ::esurfing::proto::math::Quaterniond& _internal_rotation() const;
  public:
  const ::esurfing::proto::math::Quaterniond& rotation() const;
  ::esurfing::proto::math::Quaterniond* release_rotation();
  ::esurfing::proto::math::Quaterniond* mutable_rotation();
  void set_allocated_rotation(::esurfing::proto::math::Quaterniond* rotation);

  // .esurfing.proto.math.Vector3d translation = 2;
  bool has_translation() const;
  void clear_translation();
  static const int kTranslationFieldNumber = 2;
  private:
  const ::esurfing::proto::math::Vector3d& _internal_translation() const;
  public:
  const ::esurfing::proto::math::Vector3d& translation() const;
  ::esurfing::proto::math::Vector3d* release_translation();
  ::esurfing::proto::math::Vector3d* mutable_translation();
  void set_allocated_translation(::esurfing::proto::math::Vector3d* translation);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Transformation3d)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::esurfing::proto::math::Quaterniond* rotation_;
  ::esurfing::proto::math::Vector3d* translation_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Transformation3f : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Transformation3f) */ {
 public:
  Transformation3f();
  virtual ~Transformation3f();

  Transformation3f(const Transformation3f& from);

  inline Transformation3f& operator=(const Transformation3f& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Transformation3f(Transformation3f&& from) noexcept
    : Transformation3f() {
    *this = ::std::move(from);
  }

  inline Transformation3f& operator=(Transformation3f&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Transformation3f& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Transformation3f* internal_default_instance() {
    return reinterpret_cast<const Transformation3f*>(
               &_Transformation3f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void Swap(Transformation3f* other);
  friend void swap(Transformation3f& a, Transformation3f& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Transformation3f* New() const final {
    return CreateMaybeMessage<Transformation3f>(NULL);
  }

  Transformation3f* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Transformation3f>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Transformation3f& from);
  void MergeFrom(const Transformation3f& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Transformation3f* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .esurfing.proto.math.Quaternionf rotation = 1;
  bool has_rotation() const;
  void clear_rotation();
  static const int kRotationFieldNumber = 1;
  private:
  const ::esurfing::proto::math::Quaternionf& _internal_rotation() const;
  public:
  const ::esurfing::proto::math::Quaternionf& rotation() const;
  ::esurfing::proto::math::Quaternionf* release_rotation();
  ::esurfing::proto::math::Quaternionf* mutable_rotation();
  void set_allocated_rotation(::esurfing::proto::math::Quaternionf* rotation);

  // .esurfing.proto.math.Vector3f translation = 2;
  bool has_translation() const;
  void clear_translation();
  static const int kTranslationFieldNumber = 2;
  private:
  const ::esurfing::proto::math::Vector3f& _internal_translation() const;
  public:
  const ::esurfing::proto::math::Vector3f& translation() const;
  ::esurfing::proto::math::Vector3f* release_translation();
  ::esurfing::proto::math::Vector3f* mutable_translation();
  void set_allocated_translation(::esurfing::proto::math::Vector3f* translation);

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Transformation3f)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::esurfing::proto::math::Quaternionf* rotation_;
  ::esurfing::proto::math::Vector3f* translation_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_proto_2fmath_2fgeo_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Vector3i

// int64 x = 1;
inline void Vector3i::clear_x() {
  x_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Vector3i::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3i.x)
  return x_;
}
inline void Vector3i::set_x(::google::protobuf::int64 value) {
  
  x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3i.x)
}

// int64 y = 2;
inline void Vector3i::clear_y() {
  y_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Vector3i::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3i.y)
  return y_;
}
inline void Vector3i::set_y(::google::protobuf::int64 value) {
  
  y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3i.y)
}

// int64 z = 3;
inline void Vector3i::clear_z() {
  z_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Vector3i::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3i.z)
  return z_;
}
inline void Vector3i::set_z(::google::protobuf::int64 value) {
  
  z_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3i.z)
}

// -------------------------------------------------------------------

// Polyline

// repeated .esurfing.proto.math.Vector3d points = 1;
inline int Polyline::points_size() const {
  return points_.size();
}
inline void Polyline::clear_points() {
  points_.Clear();
}
inline ::esurfing::proto::math::Vector3d* Polyline::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Polyline.points)
  return points_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
Polyline::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.math.Polyline.points)
  return &points_;
}
inline const ::esurfing::proto::math::Vector3d& Polyline::points(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Polyline.points)
  return points_.Get(index);
}
inline ::esurfing::proto::math::Vector3d* Polyline::add_points() {
  // @@protoc_insertion_point(field_add:esurfing.proto.math.Polyline.points)
  return points_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
Polyline::points() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.math.Polyline.points)
  return points_;
}

// -------------------------------------------------------------------

// Polygon

// repeated .esurfing.proto.math.Vector2f points = 1;
inline int Polygon::points_size() const {
  return points_.size();
}
inline void Polygon::clear_points() {
  points_.Clear();
}
inline ::esurfing::proto::math::Vector2f* Polygon::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Polygon.points)
  return points_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector2f >*
Polygon::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.math.Polygon.points)
  return &points_;
}
inline const ::esurfing::proto::math::Vector2f& Polygon::points(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Polygon.points)
  return points_.Get(index);
}
inline ::esurfing::proto::math::Vector2f* Polygon::add_points() {
  // @@protoc_insertion_point(field_add:esurfing.proto.math.Polygon.points)
  return points_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector2f >&
Polygon::points() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.math.Polygon.points)
  return points_;
}

// -------------------------------------------------------------------

// Points

// repeated .esurfing.proto.math.Vector3d points = 1;
inline int Points::points_size() const {
  return points_.size();
}
inline void Points::clear_points() {
  points_.Clear();
}
inline ::esurfing::proto::math::Vector3d* Points::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Points.points)
  return points_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
Points::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.math.Points.points)
  return &points_;
}
inline const ::esurfing::proto::math::Vector3d& Points::points(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Points.points)
  return points_.Get(index);
}
inline ::esurfing::proto::math::Vector3d* Points::add_points() {
  // @@protoc_insertion_point(field_add:esurfing.proto.math.Points.points)
  return points_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
Points::points() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.math.Points.points)
  return points_;
}

// -------------------------------------------------------------------

// Vector2d

// double x = 1;
inline void Vector2d::clear_x() {
  x_ = 0;
}
inline double Vector2d::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2d.x)
  return x_;
}
inline void Vector2d::set_x(double value) {
  
  x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2d.x)
}

// double y = 2;
inline void Vector2d::clear_y() {
  y_ = 0;
}
inline double Vector2d::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2d.y)
  return y_;
}
inline void Vector2d::set_y(double value) {
  
  y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2d.y)
}

// -------------------------------------------------------------------

// Vector2f

// float x = 1;
inline void Vector2f::clear_x() {
  x_ = 0;
}
inline float Vector2f::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2f.x)
  return x_;
}
inline void Vector2f::set_x(float value) {
  
  x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2f.x)
}

// float y = 2;
inline void Vector2f::clear_y() {
  y_ = 0;
}
inline float Vector2f::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2f.y)
  return y_;
}
inline void Vector2f::set_y(float value) {
  
  y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2f.y)
}

// -------------------------------------------------------------------

// Vector3d

// double x = 1;
inline void Vector3d::clear_x() {
  x_ = 0;
}
inline double Vector3d::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3d.x)
  return x_;
}
inline void Vector3d::set_x(double value) {
  
  x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3d.x)
}

// double y = 2;
inline void Vector3d::clear_y() {
  y_ = 0;
}
inline double Vector3d::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3d.y)
  return y_;
}
inline void Vector3d::set_y(double value) {
  
  y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3d.y)
}

// double z = 3;
inline void Vector3d::clear_z() {
  z_ = 0;
}
inline double Vector3d::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3d.z)
  return z_;
}
inline void Vector3d::set_z(double value) {
  
  z_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3d.z)
}

// -------------------------------------------------------------------

// Vector3f

// float x = 1;
inline void Vector3f::clear_x() {
  x_ = 0;
}
inline float Vector3f::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3f.x)
  return x_;
}
inline void Vector3f::set_x(float value) {
  
  x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3f.x)
}

// float y = 2;
inline void Vector3f::clear_y() {
  y_ = 0;
}
inline float Vector3f::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3f.y)
  return y_;
}
inline void Vector3f::set_y(float value) {
  
  y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3f.y)
}

// float z = 3;
inline void Vector3f::clear_z() {
  z_ = 0;
}
inline float Vector3f::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3f.z)
  return z_;
}
inline void Vector3f::set_z(float value) {
  
  z_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3f.z)
}

// -------------------------------------------------------------------

// Matrix2d

// double e00 = 1;
inline void Matrix2d::clear_e00() {
  e00_ = 0;
}
inline double Matrix2d::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e00)
  return e00_;
}
inline void Matrix2d::set_e00(double value) {
  
  e00_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e00)
}

// double e01 = 2;
inline void Matrix2d::clear_e01() {
  e01_ = 0;
}
inline double Matrix2d::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e01)
  return e01_;
}
inline void Matrix2d::set_e01(double value) {
  
  e01_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e01)
}

// double e10 = 3;
inline void Matrix2d::clear_e10() {
  e10_ = 0;
}
inline double Matrix2d::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e10)
  return e10_;
}
inline void Matrix2d::set_e10(double value) {
  
  e10_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e10)
}

// double e11 = 4;
inline void Matrix2d::clear_e11() {
  e11_ = 0;
}
inline double Matrix2d::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e11)
  return e11_;
}
inline void Matrix2d::set_e11(double value) {
  
  e11_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e11)
}

// -------------------------------------------------------------------

// Matrix2f

// float e00 = 1;
inline void Matrix2f::clear_e00() {
  e00_ = 0;
}
inline float Matrix2f::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e00)
  return e00_;
}
inline void Matrix2f::set_e00(float value) {
  
  e00_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e00)
}

// float e01 = 2;
inline void Matrix2f::clear_e01() {
  e01_ = 0;
}
inline float Matrix2f::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e01)
  return e01_;
}
inline void Matrix2f::set_e01(float value) {
  
  e01_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e01)
}

// float e10 = 3;
inline void Matrix2f::clear_e10() {
  e10_ = 0;
}
inline float Matrix2f::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e10)
  return e10_;
}
inline void Matrix2f::set_e10(float value) {
  
  e10_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e10)
}

// float e11 = 4;
inline void Matrix2f::clear_e11() {
  e11_ = 0;
}
inline float Matrix2f::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e11)
  return e11_;
}
inline void Matrix2f::set_e11(float value) {
  
  e11_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e11)
}

// -------------------------------------------------------------------

// Matrix3d

// double e00 = 1;
inline void Matrix3d::clear_e00() {
  e00_ = 0;
}
inline double Matrix3d::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e00)
  return e00_;
}
inline void Matrix3d::set_e00(double value) {
  
  e00_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e00)
}

// double e01 = 2;
inline void Matrix3d::clear_e01() {
  e01_ = 0;
}
inline double Matrix3d::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e01)
  return e01_;
}
inline void Matrix3d::set_e01(double value) {
  
  e01_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e01)
}

// double e02 = 3;
inline void Matrix3d::clear_e02() {
  e02_ = 0;
}
inline double Matrix3d::e02() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e02)
  return e02_;
}
inline void Matrix3d::set_e02(double value) {
  
  e02_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e02)
}

// double e10 = 4;
inline void Matrix3d::clear_e10() {
  e10_ = 0;
}
inline double Matrix3d::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e10)
  return e10_;
}
inline void Matrix3d::set_e10(double value) {
  
  e10_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e10)
}

// double e11 = 5;
inline void Matrix3d::clear_e11() {
  e11_ = 0;
}
inline double Matrix3d::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e11)
  return e11_;
}
inline void Matrix3d::set_e11(double value) {
  
  e11_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e11)
}

// double e12 = 6;
inline void Matrix3d::clear_e12() {
  e12_ = 0;
}
inline double Matrix3d::e12() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e12)
  return e12_;
}
inline void Matrix3d::set_e12(double value) {
  
  e12_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e12)
}

// double e20 = 7;
inline void Matrix3d::clear_e20() {
  e20_ = 0;
}
inline double Matrix3d::e20() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e20)
  return e20_;
}
inline void Matrix3d::set_e20(double value) {
  
  e20_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e20)
}

// double e21 = 8;
inline void Matrix3d::clear_e21() {
  e21_ = 0;
}
inline double Matrix3d::e21() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e21)
  return e21_;
}
inline void Matrix3d::set_e21(double value) {
  
  e21_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e21)
}

// double e22 = 9;
inline void Matrix3d::clear_e22() {
  e22_ = 0;
}
inline double Matrix3d::e22() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e22)
  return e22_;
}
inline void Matrix3d::set_e22(double value) {
  
  e22_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e22)
}

// -------------------------------------------------------------------

// Matrix3f

// float e00 = 1;
inline void Matrix3f::clear_e00() {
  e00_ = 0;
}
inline float Matrix3f::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e00)
  return e00_;
}
inline void Matrix3f::set_e00(float value) {
  
  e00_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e00)
}

// float e01 = 2;
inline void Matrix3f::clear_e01() {
  e01_ = 0;
}
inline float Matrix3f::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e01)
  return e01_;
}
inline void Matrix3f::set_e01(float value) {
  
  e01_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e01)
}

// float e02 = 3;
inline void Matrix3f::clear_e02() {
  e02_ = 0;
}
inline float Matrix3f::e02() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e02)
  return e02_;
}
inline void Matrix3f::set_e02(float value) {
  
  e02_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e02)
}

// float e10 = 4;
inline void Matrix3f::clear_e10() {
  e10_ = 0;
}
inline float Matrix3f::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e10)
  return e10_;
}
inline void Matrix3f::set_e10(float value) {
  
  e10_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e10)
}

// float e11 = 5;
inline void Matrix3f::clear_e11() {
  e11_ = 0;
}
inline float Matrix3f::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e11)
  return e11_;
}
inline void Matrix3f::set_e11(float value) {
  
  e11_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e11)
}

// float e12 = 6;
inline void Matrix3f::clear_e12() {
  e12_ = 0;
}
inline float Matrix3f::e12() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e12)
  return e12_;
}
inline void Matrix3f::set_e12(float value) {
  
  e12_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e12)
}

// float e20 = 7;
inline void Matrix3f::clear_e20() {
  e20_ = 0;
}
inline float Matrix3f::e20() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e20)
  return e20_;
}
inline void Matrix3f::set_e20(float value) {
  
  e20_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e20)
}

// float e21 = 8;
inline void Matrix3f::clear_e21() {
  e21_ = 0;
}
inline float Matrix3f::e21() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e21)
  return e21_;
}
inline void Matrix3f::set_e21(float value) {
  
  e21_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e21)
}

// float e22 = 9;
inline void Matrix3f::clear_e22() {
  e22_ = 0;
}
inline float Matrix3f::e22() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e22)
  return e22_;
}
inline void Matrix3f::set_e22(float value) {
  
  e22_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e22)
}

// -------------------------------------------------------------------

// Quaterniond

// double w = 1;
inline void Quaterniond::clear_w() {
  w_ = 0;
}
inline double Quaterniond::w() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.w)
  return w_;
}
inline void Quaterniond::set_w(double value) {
  
  w_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.w)
}

// double x = 2;
inline void Quaterniond::clear_x() {
  x_ = 0;
}
inline double Quaterniond::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.x)
  return x_;
}
inline void Quaterniond::set_x(double value) {
  
  x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.x)
}

// double y = 3;
inline void Quaterniond::clear_y() {
  y_ = 0;
}
inline double Quaterniond::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.y)
  return y_;
}
inline void Quaterniond::set_y(double value) {
  
  y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.y)
}

// double z = 4;
inline void Quaterniond::clear_z() {
  z_ = 0;
}
inline double Quaterniond::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.z)
  return z_;
}
inline void Quaterniond::set_z(double value) {
  
  z_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.z)
}

// -------------------------------------------------------------------

// Quaternionf

// float w = 1;
inline void Quaternionf::clear_w() {
  w_ = 0;
}
inline float Quaternionf::w() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.w)
  return w_;
}
inline void Quaternionf::set_w(float value) {
  
  w_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.w)
}

// float x = 2;
inline void Quaternionf::clear_x() {
  x_ = 0;
}
inline float Quaternionf::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.x)
  return x_;
}
inline void Quaternionf::set_x(float value) {
  
  x_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.x)
}

// float y = 3;
inline void Quaternionf::clear_y() {
  y_ = 0;
}
inline float Quaternionf::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.y)
  return y_;
}
inline void Quaternionf::set_y(float value) {
  
  y_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.y)
}

// float z = 4;
inline void Quaternionf::clear_z() {
  z_ = 0;
}
inline float Quaternionf::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.z)
  return z_;
}
inline void Quaternionf::set_z(float value) {
  
  z_ = value;
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.z)
}

// -------------------------------------------------------------------

// Transformation3d

// .esurfing.proto.math.Quaterniond rotation = 1;
inline bool Transformation3d::has_rotation() const {
  return this != internal_default_instance() && rotation_ != NULL;
}
inline void Transformation3d::clear_rotation() {
  if (GetArenaNoVirtual() == NULL && rotation_ != NULL) {
    delete rotation_;
  }
  rotation_ = NULL;
}
inline const ::esurfing::proto::math::Quaterniond& Transformation3d::_internal_rotation() const {
  return *rotation_;
}
inline const ::esurfing::proto::math::Quaterniond& Transformation3d::rotation() const {
  const ::esurfing::proto::math::Quaterniond* p = rotation_;
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3d.rotation)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Quaterniond*>(
      &::esurfing::proto::math::_Quaterniond_default_instance_);
}
inline ::esurfing::proto::math::Quaterniond* Transformation3d::release_rotation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3d.rotation)
  
  ::esurfing::proto::math::Quaterniond* temp = rotation_;
  rotation_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Quaterniond* Transformation3d::mutable_rotation() {
  
  if (rotation_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Quaterniond>(GetArenaNoVirtual());
    rotation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3d.rotation)
  return rotation_;
}
inline void Transformation3d::set_allocated_rotation(::esurfing::proto::math::Quaterniond* rotation) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete rotation_;
  }
  if (rotation) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      rotation = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, rotation, submessage_arena);
    }
    
  } else {
    
  }
  rotation_ = rotation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3d.rotation)
}

// .esurfing.proto.math.Vector3d translation = 2;
inline bool Transformation3d::has_translation() const {
  return this != internal_default_instance() && translation_ != NULL;
}
inline void Transformation3d::clear_translation() {
  if (GetArenaNoVirtual() == NULL && translation_ != NULL) {
    delete translation_;
  }
  translation_ = NULL;
}
inline const ::esurfing::proto::math::Vector3d& Transformation3d::_internal_translation() const {
  return *translation_;
}
inline const ::esurfing::proto::math::Vector3d& Transformation3d::translation() const {
  const ::esurfing::proto::math::Vector3d* p = translation_;
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3d.translation)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Vector3d*>(
      &::esurfing::proto::math::_Vector3d_default_instance_);
}
inline ::esurfing::proto::math::Vector3d* Transformation3d::release_translation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3d.translation)
  
  ::esurfing::proto::math::Vector3d* temp = translation_;
  translation_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Vector3d* Transformation3d::mutable_translation() {
  
  if (translation_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Vector3d>(GetArenaNoVirtual());
    translation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3d.translation)
  return translation_;
}
inline void Transformation3d::set_allocated_translation(::esurfing::proto::math::Vector3d* translation) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete translation_;
  }
  if (translation) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      translation = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, translation, submessage_arena);
    }
    
  } else {
    
  }
  translation_ = translation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3d.translation)
}

// -------------------------------------------------------------------

// Transformation3f

// .esurfing.proto.math.Quaternionf rotation = 1;
inline bool Transformation3f::has_rotation() const {
  return this != internal_default_instance() && rotation_ != NULL;
}
inline void Transformation3f::clear_rotation() {
  if (GetArenaNoVirtual() == NULL && rotation_ != NULL) {
    delete rotation_;
  }
  rotation_ = NULL;
}
inline const ::esurfing::proto::math::Quaternionf& Transformation3f::_internal_rotation() const {
  return *rotation_;
}
inline const ::esurfing::proto::math::Quaternionf& Transformation3f::rotation() const {
  const ::esurfing::proto::math::Quaternionf* p = rotation_;
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3f.rotation)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Quaternionf*>(
      &::esurfing::proto::math::_Quaternionf_default_instance_);
}
inline ::esurfing::proto::math::Quaternionf* Transformation3f::release_rotation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3f.rotation)
  
  ::esurfing::proto::math::Quaternionf* temp = rotation_;
  rotation_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Quaternionf* Transformation3f::mutable_rotation() {
  
  if (rotation_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Quaternionf>(GetArenaNoVirtual());
    rotation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3f.rotation)
  return rotation_;
}
inline void Transformation3f::set_allocated_rotation(::esurfing::proto::math::Quaternionf* rotation) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete rotation_;
  }
  if (rotation) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      rotation = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, rotation, submessage_arena);
    }
    
  } else {
    
  }
  rotation_ = rotation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3f.rotation)
}

// .esurfing.proto.math.Vector3f translation = 2;
inline bool Transformation3f::has_translation() const {
  return this != internal_default_instance() && translation_ != NULL;
}
inline void Transformation3f::clear_translation() {
  if (GetArenaNoVirtual() == NULL && translation_ != NULL) {
    delete translation_;
  }
  translation_ = NULL;
}
inline const ::esurfing::proto::math::Vector3f& Transformation3f::_internal_translation() const {
  return *translation_;
}
inline const ::esurfing::proto::math::Vector3f& Transformation3f::translation() const {
  const ::esurfing::proto::math::Vector3f* p = translation_;
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3f.translation)
  return p != NULL ? *p : *reinterpret_cast<const ::esurfing::proto::math::Vector3f*>(
      &::esurfing::proto::math::_Vector3f_default_instance_);
}
inline ::esurfing::proto::math::Vector3f* Transformation3f::release_translation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3f.translation)
  
  ::esurfing::proto::math::Vector3f* temp = translation_;
  translation_ = NULL;
  return temp;
}
inline ::esurfing::proto::math::Vector3f* Transformation3f::mutable_translation() {
  
  if (translation_ == NULL) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Vector3f>(GetArenaNoVirtual());
    translation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3f.translation)
  return translation_;
}
inline void Transformation3f::set_allocated_translation(::esurfing::proto::math::Vector3f* translation) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete translation_;
  }
  if (translation) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      translation = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, translation, submessage_arena);
    }
    
  } else {
    
  }
  translation_ = translation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3f.translation)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace math
}  // namespace proto
}  // namespace esurfing

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_proto_2fmath_2fgeo_2eproto
