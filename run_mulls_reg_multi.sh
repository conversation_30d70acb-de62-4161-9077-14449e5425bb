#!/bin/sh
#########################################################################################
#                     MULLS multi-point cloud registration                             #
############################# part to configure (down)###################################

# Comma-separated list of point cloud paths (*.pcd, *.las, *.ply, *.txt, *.h5, *.bin)
point_cloud_paths="./demo_data/pcd/DE.pcd,./demo_data/pcd/BW.pcd,./demo_data/pcd/DB.pcd,./demo_data/pcd/AB.pcd"
reference_point_cloud_idx=0
output_point_cloud_path="./demo_data/result/multi_merged.pcd"

# Run the multi-point cloud registration
./bin/mulls_reg_multi \
--colorlogtostderr=true \
-stderrthreshold 0 \
-log_dir ./log \
--v=10 \
--point_cloud_paths="${point_cloud_paths}" \
--reference_point_cloud_idx=${reference_point_cloud_idx} \
--output_point_cloud_path=${output_point_cloud_path} \
--realtime_viewer_on=false \
--cloud_down_res=0.05 \
--gf_grid_size=3.0 \
--gf_in_grid_h_thre=0.3 \
--gf_neigh_grid_h_thre=1.5 \
--dist_inverse_sampling_method=1 \
--unit_dist=50.0 \
--gf_ground_down_rate=8 \
--gf_nonground_down_rate=3 \
--pca_neighbor_radius=1.8 \
--pca_neighbor_count=50 \
--linearity_thre=0.65 \
--planarity_thre=0.65 \
--curvature_thre=0.1 \
--reciprocal_corr_on=true \
--corr_dis_thre=3.0 \
--converge_tran=0.0005 \
--converge_rot_d=0.002 \
--reg_max_iter_num=2000 \
--teaser_on=false